using System.Threading.Tasks;
using Razor.Logic.MqServices.Tax.Exceptions;
using Razor.Logic.MqServices.Tax.Models;

namespace Razor.Logic.MqServices.Tax.Services.ByTransaction.Avalara
{
    /// <summary>
    /// Provides services for handling Avalara tax transactions, including applying tax from transactions and managing Avalara transaction lifecycle.
    /// </summary>
    internal interface IAvalaraService
    {
        /// <summary>
        /// Applies tax to a sales order by processing the corresponding Avalara transaction.
        /// </summary>
        /// <param name="request">The tax application request containing sales order and invoice information.</param>
        /// <returns>A <see cref="TaxApplicationResult"/> indicating whether tax was successfully applied.</returns>
        /// <exception cref="TaxCalculationException">Thrown when Avalara tax calculation fails.</exception>
        Task<TaxApplicationResult> ApplyTaxFromTransactionAsync(TaxApplicationRequest request);
        
        /// <summary>
        /// Ensures that Avalara client is available and authenticated
        /// </summary>
        /// <returns>A flag that indicates that Avalara is available</returns>
        Task<bool> IsAvailableAsync();
    }
}