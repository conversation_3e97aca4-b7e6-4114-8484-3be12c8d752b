@using Razor.Logic.Services.LogicServices.Interfaces.RecyclingOrders.Models
@model RecyclingReportPdfHeader
@{
    RecyclingReportPdfHeader m = Model;
}


<!doctype html>
<html>
<head>
<title>Header | Razor-pdf</title>
<meta charset="utf-8">
<style>

 /* clearfix */
    .clearfix:before, 
    .clearfix:after {
        content: "";
        display: table;
    }
        .clearfix:after {
            clear: both;
        }
            .clearfix {
                zoom: 1;
            }
 /* clearfix */
    /*html {
       border: 1px solid #000;
    }*/
    body, html {
        padding: 0;
        margin: 0;
        height: 168px;
        background: transparent;
        
    }
        .wrapper {
            padding: 0;
            width: 992px;
            position: relative;
            margin: auto;
            font-family: arial;
            height: 100%;
            min-height: 100%;
            background: transparent;
        }
            .header {
                height: 140px;
                width: 100%;
                position: absolute;
                bottom: 0px;
                margin-left: -496px;
                left: 50%;
                vertical-align: top;
                background: transparent;
            }
                .header a {
                    background-repeat: no-repeat;
                    display: block;
                    height: 80px;
                    background-size: 90% auto;
                }
                    /*.header a img {
                        width: 100%;
                    }*/

        .logoWr {
            float: left;
            margin: 0 0 0 8px;
        }
            .logo {
                width: 300px;
                margin: 30px 0 0 0;
            }
				.logo img {
					height: auto;
					width: auto;
					max-height: 100%;
					max-width: 100%;
				}
            .faxPhone {
                margin: 4px 0 0 10px;
                font-size: 18px;
            }
                .fax {
                    float: left;
                    display: block;
                    margin: 0 0 0 20px;
                }
                .phone {
                    float: left;
                    display: block;
                }
        .r2 {
            float: left;
            margin: 30px 0 0 20px;
        }
        .certif-firm {
            float: left;
            width: 280px;
            margin: 19px 0 0 50px;
        }
        .invoice-block {
            height: 100px;
            width: 480px;
            margin: 30px 20px 0 0;
            background: transparent;
            float: right;
        }
            .invoice-block p {
                text-align: right;
                margin: 0px 10px 0px 0;
                font-weight: bold;
                background: transparent;
            }
                .invoice-block p.invoice {
                    font-size: 56px;
                    line-height: 66px;
                    font-weight: bold;
                    color: #999;
                    margin: 0px 0 0 0;
                    letter-spacing: 0px;
                    text-align: right;
                    background: transparent;
                }
                .invoice-block p.completeP {
                    color: #999;
                    font-size: 22px;
                    line-height: 42px;
                    font-weight: normal;
                    text-align: right;
                    background: transparent;
                }
    </style>
</head>
<body>
    <div class="wrapper">

        <div class="header">

            <div class="logoWr">

                @*<a class="logo" style="background-image: url('@(m.HeaderUrl)');"></a>*@
				<a class="logo">
					<img src="@m.HeaderUrl" alt="">
				</a>

                <p class="faxPhone clearfix">
                    <span class="phone"> @m.Phone</span>
                    <span class="fax"> @m.Fax</span>
                </p>

            </div>           

            <div class="invoice-block">

                <p class="invoice">Outbound Report</p>

                <p class="completeP">@m.AutoName</p>

            </div>

        </div>

    </div>
</body>
</html>