using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Razor.FileSystemUtility.S3.Adapters.NativeS3.Dto;

namespace Razor.FileSystemUtility.S3.Adapters.NativeS3
{
    public interface IS3Adapter
    {
        Task<S3File> GetObjectAsync(S3Address a, CancellationToken ct);
        Task PutObjectAsync(S3Address a, S3File file, CancellationToken ct);
        Task CppyObjectAsync(S3Address src, S3Address dest, CancellationToken ct);
        Task DeleteObjectAsync(S3Address a, CancellationToken ct);
        Task DeleteObjectsAsync(string bucket, IEnumerable<string> objectNamePaths, CancellationToken ct);
        Task<bool> BucketExistsAsync(S3Address a, CancellationToken ct);
        Task<bool> ObjectExistsAsync(S3Address a, CancellationToken ct);
    }
}
