Ractive.components.importfile = Ractive.extend({
    template: Url.GetTemplate(Common.baseUrl + '/Scripts/Controls/Ractive/Common/Import/importfile.html'),

    _urls: {
        downloadXlsTemplate: Common.baseUrl + '/Content/ImportTemplates/',
    },

    Events: {
        importComplete: 'importcomplete',
        importStarted: 'importStarted'
    },

    _controls: {
        importProgress: null
    },

    data: function () {
        const r = this;

        return {
            isEnabled: function () {
                const entityId = r.get('entityId');
                const isDisabled = r.get('isDisabled') || 0;
                return entityId && !isDisabled;
            },
            logic: {
                File: [],
                OutputFileName: files => (files && files[0] && files[0].name) || 'File not selected'
            }
        }
    },

    oninit: function () {
        var r = this;

        r.setData();

        r.on({
            // Import uploader
            importOrder: function (event) {

                if ($.isFunction(r.Events.importStarted))
                    invokeEvent(r.Events.importStarted);
                else
                    r.fire(r.Events.importStarted);

                var entityId = r.get('entityId');
                if (!Number.parse(entityId)) {
                    NoticeAlert.showWarning('Import failed', 'Order not selected');
                    r.discard();
                    return;
                }

                var files = r.get('logic.File');

                // no files
                if (!(files && files[0] && files[0].name)) {
                    NoticeAlert.showWarning('Import failed', 'No file selected');
                    r.discard();
                    return;
                }

                var file = files[0];
                // not xls - return;
                if (!(new RegExp('.xlsx$').test(file.name) || new RegExp('.xls$').test(file.name))) {
                    NoticeAlert.showWarning('Wrong file extension', 'The provided file is not recognized as an Excel file.');
                    r.discard();
                    return;
                }

                ShowConfirm(String.format('Items will be imported from "{0}".<br/>Continue?', file.name), null, function () {
                    if (!window.FormData) {
                        NoticeAlert.showError('Error', 'Cannot collect form data');
                        return;
                    }

                    url = r.getImportUrl() + window.location.search;

                    var data = new window.FormData();
                    data.append("entityId", entityId);
                    data.append("autoName", r.get('autoName'));
                    data.append("CallbackUrl", url);
                    data.append("ImportFile", file);

                    var response = Url.PostFileSync(url, data);
                    ProcessAjaxItemResponse(response, function (res) {
                        if (r.get('openProgress'))
                            r.openImportProgress();

                        NoticeAlert.showSuccess('Order Import', res);
                    },
                        function (res) {
                            NoticeAlert.showWarning('Order Import', res);
                        }
                    );

                    r.discard();
                });
            },
            downloadFile: function (event) {
                const r = this;
                Url.openNewTab(r._urls.downloadXlsTemplate + r.get('fileUrl'));
            }
        });
    },

    discard: function () {
        var r = this;
        r.set('logic.File', null).then(function () {
            r.update('logic.File');
        });
        // does not work without:
        $('#fl_ImportFile').val(null).change();
    },

    setData: function () {
        let r = this;

        r.set('entityId', r.entityId || null);
        r.set('importUrl', r.importUrl || '');
        r.set('autoName', r.autoName || '');
        r.set('openProgress', r.openProgress || false);

        r.set('class', r.class || '');
        r.set('container_class', r.container_class || '');
        r.set('isDisabled', r.isDisabled);
        r.set('caption', r.caption);
        r.set('fileUrl', r.fileUrl);

        if ($.isFunction(r.importStarted))
            r.Events.importStarted = r.importStarted;

        if ($.isFunction(r.importComplete))
            r.Events.importComplete = r.importComplete;
    },

    setIsDisabled: function (isDisabled) {
        let r = this;

        r.set('isDisabled', isDisabled);
    },

    getImportUrl: function () {
        var r = this;

        return r.get('importUrl');
    },   

    //Connection to the callbackHub and defining a callback function
    connectToCallbackHub: function () {
        const r = this;

        let url = r.getImportUrl() + window.location.search;

        CallbackServer.Connect(url, callback, progressCallback);
      
        function callback() {
            if (r.get('openProgress'))
                r._controls.importProgress.Close();                

            if ($.isFunction(r.Events.importComplete))
                invokeEvent(r.Events.importComplete);    
            else
                r.fire(r.Events.importComplete);                           
        }

        function progressCallback(step, total, stepName) {
            if (r.get('openProgress'))
                r._controls.importProgress.ShowProgress(step, total, stepName);
        }
    },

    openImportProgress: function () {
        var r = this;

        if (!r._controls.importProgress) {
            r._controls.importProgress = new Ractive.components.importprogress({
                el: 'div_DialogImportProgress'
            });
        }
        r._controls.importProgress.Open();
    }    

});