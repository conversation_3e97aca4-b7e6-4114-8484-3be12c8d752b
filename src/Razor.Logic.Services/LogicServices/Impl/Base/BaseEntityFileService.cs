using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Nito.AsyncEx;
using Razor.Common.Base.CommonModels;
using Razor.Common.Base.Context;
using Razor.Common.Core.FileProcessing.Models;
using Razor.FileStorage.FileStorage;
using Razor.Logic.Services.LogicServices.Interfaces.Base;

namespace Razor.Logic.Services.LogicServices.Impl.Base
{
    public abstract class BaseEntityFileService : IEntityFileService
    {
        #region Constructor

        public BaseEntityFileService(IEntityFileStorage entityFileStorage, IContextVariablesProvider contextVariablesProvider)
        {
            _entityFileStorage = entityFileStorage;
            _contextVariablesProvider = contextVariablesProvider;
        }

        #endregion Constructor

        #region Public methods

        public abstract IEnumerable<FileSizeInfo> ListFiles(long entityId);

        public MemoryFile GetFile(long entityId, long fileId, string fileName, CancellationToken cancellationToken) =>
            GetFile(_entityFileStorage, entityId, fileId, fileName, cancellationToken);

        public virtual MemoryFile GetThumbnail(long entityId, long fileId, string fileName, CancellationToken cancellationToken) => null;

        public FileSizeInfo SaveFile(long entityId, string fileName, byte[] fileData, object fileParams, CancellationToken cancellationToken)
        {
            var fileInfo = Validate(entityId, fileName, fileData);
            if (!string.IsNullOrWhiteSpace(fileInfo.Error))
            {
                return fileInfo;
            }

            try
            {
                SaveFile(fileInfo, fileData, fileParams, cancellationToken);
            }
            catch (Exception ex)
            {
                fileInfo.Error = ex.Message;
            }

            return fileInfo;
        }

        public void CopyFile(long entityId, long fileId, string fileName, string sourceFilePath, bool sourceFileSecured, CancellationToken cancellationToken) =>
            AsyncContext.Run(() => CopyFileAsync(entityId, fileId, fileName, sourceFilePath, sourceFileSecured, cancellationToken));

        public virtual void DeleteFile(long entityId, long fileId, string fileName, CancellationToken cancellationToken)
        {
            DeleteFileRecord(fileId);
            DeleteFileFromStorage(entityId, fileId, fileName, cancellationToken);
        }

        public string GetFilePath(long entityId, long fileId, string fileName) =>
            _entityFileStorage.GetFilePath(CompanyId, entityId, GetStorageFileName(fileId, fileName));

        #endregion Public methods

        #region Protected methods

        protected MemoryFile GetFile(IEntityFileStorage entityFileStorage, long entityId, long fileId, string fileName, CancellationToken cancellationToken)
        {
            var entityFile = new MemoryFile
            {
                Id = fileId,
                FileName = fileName
            };

            if (string.IsNullOrWhiteSpace(fileName))
            {
                entityFile.Error = "The file name is missing";
                return entityFile;
            }

            try
            {
                entityFile.FileNameOnDisk = GetStorageFileName(fileId, fileName);
                var fileData = AsyncContext.Run(() => entityFileStorage.ReadFileAsync(CompanyId, entityId, entityFile.FileNameOnDisk, cancellationToken));
                if (fileData?.Data != null)
                {
                    entityFile.Bytes = fileData.Data;
                    entityFile.LastWriteTime = fileData.LastModified;
                }
                else
                {
                    entityFile.Error = $"The file {entityFile.FileNameOnDisk} is not found in storage";
                }
            }
            catch (Exception ex)
            {
                entityFile.Error = ex.Message;
            }

            return entityFile;
        }

        protected abstract FileSizeInfo AddFileRecord(long entityId, string fileName, long fileSizeBytes, long thumbnailSizeBytes, object fileParams);

        protected abstract void DeleteFileRecord(long fileId);

        protected virtual void SaveFile(FileSizeInfo fileInfo, byte[] fileData, object fileParams, CancellationToken cancellationToken)
        {
            var newFileRecord = AddFileRecord(fileInfo.OwnerId, fileInfo.Name, fileInfo.Length, fileInfo.ThumbLength, fileParams);

            fileInfo.Id = newFileRecord?.Id;
            fileInfo.Name = newFileRecord?.Name ?? fileInfo.Name;

            if (fileInfo.Id.HasValue)
            {
                try
                {
                    WriteFileToStorage(fileInfo.OwnerId, fileInfo.Id.Value, fileInfo.Name, fileData, cancellationToken);
                }
                catch
                {
                    DeleteFileRecord(fileInfo.Id.Value);
                    throw;
                }
            }
            else
            {
                fileInfo.Error = "Failed to add file record to database";
            }
        }

        protected void WriteFileToStorage(long entityId, long fileId, string fileName, byte[] fileData, CancellationToken cancellationToken) =>
            AsyncContext.Run(() => WriteFileToStorageAsync(entityId, fileId, fileName, fileData, cancellationToken));

        protected virtual void DeleteFileFromStorage(long entityId, long fileId, string fileName, CancellationToken cancellationToken) =>
            AsyncContext.Run(() => _entityFileStorage.DeleteFileAsync(CompanyId, entityId, GetStorageFileName(fileId, fileName), cancellationToken));

        #endregion Protected methods

        #region Private methods

        private FileSizeInfo Validate(long entityId, string fileName, byte[] fileData)
        {
            var fileInfo = new FileSizeInfo(entityId);

            if (!string.IsNullOrWhiteSpace(fileName))
            {
                fileInfo.Name = fileName;
            }
            else
            {
                fileInfo.Error = "The file name is missing";
                return fileInfo;
            }

            if (fileData != null && fileData.Length > 0)
            {
                fileInfo.Length = fileData.Length;
            }
            else
            {
                fileInfo.Error = "The file data is missing";
                return fileInfo;
            }

            return fileInfo;
        }

        private async Task WriteFileToStorageAsync(long entityId, long fileId, string fileName, byte[] fileData, CancellationToken cancellationToken)
        {
            using (var ms = new MemoryStream(fileData))
            {
                await _entityFileStorage.WriteFileAsync(CompanyId, entityId, GetStorageFileName(fileId, fileName), ms, cancellationToken);
            }
        }

        private Task CopyFileAsync(long entityId, long fileId, string fileName, string sourceFilePath, bool sourceFileSecured, CancellationToken cancellationToken) =>
            _entityFileStorage.CopyFileAsync(CompanyId, entityId, GetStorageFileName(fileId, fileName), sourceFilePath, sourceFileSecured, cancellationToken);

        protected static string GetStorageFileName(long fileId, string fileName) => $"{fileId}_{fileName}";

        #endregion Private methods

        #region Private fields and properties

        private readonly IEntityFileStorage _entityFileStorage;
        private readonly IContextVariablesProvider _contextVariablesProvider;

        private long CompanyId => _contextVariablesProvider.ContextVariables.CompanyId;

        #endregion Private fields and properties
    }
}
