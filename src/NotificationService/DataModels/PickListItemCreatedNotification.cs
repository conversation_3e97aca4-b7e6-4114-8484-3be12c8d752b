using System;
using NotificationService.DataModels.NotificationDataParts;
using Razor.Common.Base.Attributes;
using Razor.Common.Core.Snippets;

namespace NotificationService.DataModels
{
    class PickListItemCreatedNotification : NotificationBase
    {
        [Snippets]
        public UserNotificationPart RepUser { get; set; }
        [Snippets]
        public PickListNotificationPart PickList { get; set; }

    }
}
