using System;
using System.Linq;
using AutoMapper;
using Razor.Common.Base.Context;
using Razor.Common.Base.Context.Data;
using Razor.Common.Base.Helpers;
using Razor.Common.Core.Addresses;
using Razor.Common.Core.Company.Data;
using Razor.Common.Core.Currency.Context.Populators;
using Razor.Common.Core.Currency.Converter;
using Razor.Common.Core.Currency.DataServices;
using Razor.Data.Core.DataServices.Addresses;
using Razor.Data.Core.DataServices.Customer;
using Razor.Data.Core.DataServices.Location;
using Razor.Data.Core.DataServices.RecyclingOrder;
using Razor.Data.Core.DataServices.RecyclingOrder.Models;
using Razor.Data.Core.DataServices.RecyclingOrder.Models.Audit;
using Razor.Data.Core.DataServices.Settlement;
using Razor.Data.Core.DataServices.User;
using Razor.Data.Core.Enums;
using Razor.Logic.Common.Helpers;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Const;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Impl.Reports.RecyclingOrder.General.Base;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Impl.Reports.RecyclingOrder.General.Base.Model;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Impl.Reports.RecyclingOrder.General.Model.Inbound;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Impl.Reports.RecyclingOrder.Mappings;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Impl.Reports.RecyclingOrder.Settlement.Base.Model;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Mappers;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Model;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Services.CrmAccount;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Services.Onsite;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.Models.Base;
using Razor.Logic.Services.DocumentGeneration.Exceptions;
using Razor.Logic.Services.LogicServices.Interfaces.Customer;
using Razor.Logic.Services.LogicServices.Interfaces.InboundOrder;
using Razor.Mq.FileShare.TempFileShares.Signatures;

namespace Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Impl.Reports.RecyclingOrder.General
{
    public class InboundOrderReportDataSourceBuilder: RecyclingOrderReportDataSourceBuilder<DocumentRequestBase, InboundOrderReportDataSource>
    {
        public InboundOrderReportDataSourceBuilder(
        ILogoHelper logoHelper,
        IUserDataService userDataService,
        IContextVariablesProvider context,
        ICustomerDataService customerDataService,        
        ICompanyDataService companyDataService,
        IAddressMapper addressMapper,
        IRecyclingOrderDataService recyclingOrderDataService,
        ICustomerService customerService,
        IInboundOrderService inboundOrderService,
        IAddressService addressService,
        ILocationDataService locationDataService,
        ICurrencyConverter currencyConverter,
        IRecyclingOrderCurrencyDataService currencyDataService,
        IRecyclingOrderCurrencyExchangeContextPopulator currencyCtxPopulator,
        ICrmAccountReportService crmAccountService,
        ISettlementDataService settlementDataService,
        ITempSignatureFileShare signatureShare,
        IOnsiteReportService onsiteReportService,
        IConfigDbContextDataService dsConfigDb)
        : base(logoHelper, userDataService, context, customerDataService, companyDataService, addressMapper, 
              recyclingOrderDataService, customerService, addressService, currencyConverter, currencyDataService, currencyCtxPopulator, crmAccountService, dsConfigDb)
        {
            _inboundOrderService = inboundOrderService;
            _locationDataService = locationDataService;
            _settlementDataService = settlementDataService;
            _signatureShare = signatureShare;
            _onsiteReportService = onsiteReportService;
            _context = context;
        }

        public override InboundOrderReportDataSource Build(DocumentRequestBase rq)
        {
            var model = base.Build(rq);
            var orderId = rq.RootEntityId;
            var recyclingOrder = RecyclingOrderDataService.GetRecyclingOrder(orderId);
            if (recyclingOrder == null)
            {
                throw new RelatedEntityNotFoundException($"Recycling inbound order id: {orderId}");
            }

            var recyclingOrderPdf = RecyclingOrderDataService.GetRecyclingOrderForPdf(orderId);
            var recyclingOrderToRecieve = RecyclingOrderDataService.GetRecyclingOrderByIdToRecieve(orderId, true);

            var tz = _context.TimeZone;
            var warehouseTimeZoneId = _locationDataService.GetWarehouseTimeZoneIdByRecyclingOrderId(orderId);
            var warehouseTimeZone = string.IsNullOrEmpty(warehouseTimeZoneId) 
                ? tz : (TimeZoneInfo.FindSystemTimeZoneById(warehouseTimeZoneId) ?? tz);
            recyclingOrderPdf.PickupStartDate = recyclingOrderPdf.PickupStartDate.ConvertTimeFromUtc(warehouseTimeZone);
            if (recyclingOrderPdf.PickupEndDate.HasValue)
                recyclingOrderPdf.PickupEndDate = recyclingOrderPdf.PickupEndDate.Value.ConvertTimeFromUtc(warehouseTimeZone);

            // Addresses
            AddressModel fromAddress = recyclingOrder.AddressId.HasValue ?
                AddressService.GetAddress(recyclingOrder.AddressId.Value) : 
                null; 
            model.ShipFromAddress = AddressMapper.Map(fromAddress);

            if (recyclingOrder.ShipToAddressId.HasValue)
            {
                var toAddress = AddressService.GetAddress(recyclingOrder.ShipToAddressId.Value);
                model.ShipToAddress = AddressMapper.Map(toAddress);
                if (model.ShipToAddress != null)
                {
                    model.ShipToAddress.CompanyName = recyclingOrder.ShipToCustomerName;
                    model.ShipToAddress.ContactName = recyclingOrder.ShipToContactCd;
                }             
            }
            else
            {
                model.ShipToAddress = AddressMapper.Map((AddressModel)null);
            }

            // filling RecyclingOrderInfoBase part of InboundOrderInfo
            model.Order = new InboundOrderInfo
            {
                Customer = recyclingOrderToRecieve.CustomerName,
                CustomerCode = recyclingOrderToRecieve.CustomerCode,
                PrimaryCustomer = recyclingOrderToRecieve.PrimaryCustomerName,
                CustomerId = recyclingOrderToRecieve.CustomerId,
                RecyclingOrderNumber = recyclingOrder.AutoName,
                RecyclingOrderType = "Inbound",
                RecyclingOrderStatusId = recyclingOrderToRecieve.StatusId,
                RecyclingOrderStatus = recyclingOrderToRecieve.StatusCd,
                OrderDate = recyclingOrderPdf.PickupStartDate, //as it is in SettlementInbound
                Warehouse = recyclingOrderToRecieve.WarehouseCd,
                WorkInstruction = recyclingOrder.WorkInstructions,
                Pallets = recyclingOrder.PalletCount ?? 0,
                PalletsReceived = recyclingOrder.PalletCountReceived ?? 0
            };
            model.Order.DestructionType = RecyclingOrderDataService.ListDestructionTypes().FirstOrDefault(it => it.value == recyclingOrder.DestructionTypeId)?.label ?? "None";

            var customerAddress = AddressService.GetCustomerAddressPreferablyMain(recyclingOrderToRecieve.CustomerId, AddressTypes.ShipTo);
            model.Order.CustomerAddress = AddressMapper.Map(customerAddress);

            // filling RecyclingOrderCommonInfo part of InboundOrderInfo
            model.Order.Rep = recyclingOrder.UserName;
            model.Order.BOLNumber = recyclingOrder.BolNumber;
            model.Order.BOL = RecyclingOrderDataService.GetRecyclingOrderBOLNumber(orderId);
            model.Order.PONumber = recyclingOrder.PoNumber;
            model.Order.SOTerms = model.SOTermList.Where(x => x.value == recyclingOrder.SoTermId).Select(x => x.label).FirstOrDefault();

            // filling rest of InboundOrderInfo (will be long)
            model.Order.ReceiveStart = recyclingOrderToRecieve.ReceiveDate;
            model.Order.ReceiveComplete = recyclingOrderToRecieve.CompletedDate;
            model.Order.Weight = new InboundOrderWeightInfo
            {
                EstimatedWeight = recyclingOrder.WeightLbs,
                ReceivedWeight = recyclingOrderToRecieve.RecievedWeight,
                Tare = recyclingOrderToRecieve.TareWeight
            };
            model.Order.Condition = recyclingOrderPdf.RecyclingCondition;
            model.Order.PackageType = RecyclingOrderDataService.ListPackagingTypesExt("%%", ReportBuidlerConsts.AllAvailableRows)
                .Where(x => x.value == recyclingOrder.PackagingTypeId).Select(x => x.label).FirstOrDefault();
            model.Order.InternalComments = recyclingOrder.InternalComments;
            model.Order.Terms = recyclingOrder.Terms;
            model.Order.ReceivingNotes = recyclingOrderToRecieve.ReceivingNotes;
            model.Order.LogisticType = model.Order.LogisticType = LogisticIdToString(recyclingOrder.LogisticType);
            model.Order.Billing = RecyclingOrderDataService.ListBillingTypes().Where(x => x.value == recyclingOrder.BillingType).Select(x => x.label).FirstOrDefault();
            model.Order.ServiceType = RecyclingOrderDataService.ListServiceTypes().Where(x => x.value == recyclingOrder.ServiceType).Select(x => x.label).FirstOrDefault();
            model.Order.SLADays = recyclingOrder.SlaDays;
            model.Order.QuoteExpirationDate = recyclingOrder.QuoteExpirationDate;

            // Inbound order - Preliminary tab - Pickup details
            model.Order.PickupInfo = new InboundOrderPickupInfo
            {
                PickupDate = recyclingOrderPdf.PickupStartDate,
                Service = (string.Format(
                            recyclingOrderPdf.Duration >= 1440 ? "{0:%d} d {0:%h} hr {0:%m} min"
                            : recyclingOrderPdf.Duration >= 60 ? "{0:%h} hr {0:%m} min"
                            : "{0:%m} min", TimeSpan.FromMinutes(recyclingOrderPdf.Duration))),
                TimeWindow     = $"{recyclingOrderPdf.PickupStartDate:hh:mmtt} - {(recyclingOrderPdf.PickupEndDate ?? recyclingOrderPdf.PickupStartDate):hh:mmtt}",
                EstDelivery    = recyclingOrder.EstimatedDeliveryDate,
                ActPickupTime  = recyclingOrder.ActualPickupTime,
                ActArrivalTime = recyclingOrder.ActualArrivalTime,
                DeliveryPoint  = recyclingOrder.DeliveryPoint,
                TruckType      = RecyclingOrderDataService.ListTruckTypes().Where(x => x.value == recyclingOrder.TruckTypeId).Select(x => x.label).FirstOrDefault()
            };
            // Inbound order - Preliminary tab - onsite contact section
            model.Order.OnsiteContact = new RecyclingOrderContactInfo
            {
                Name = recyclingOrderPdf.ContactName,
                Phone = recyclingOrderPdf.MainPhone,
                Email = recyclingOrderPdf.MainEmail,
                MobilePhone = recyclingOrderPdf.MobilePhone
            };
            model.Order.AltContact = new RecyclingOrderContactInfo
            {
                Name = recyclingOrderPdf.AltContactName,
                Phone = recyclingOrderPdf.AltMainPhone,
                Email = recyclingOrderPdf.AltMainEmail,
                MobilePhone = recyclingOrderPdf.AltMobilePhone
            };
            // Inbound order - Preliminary tab - freight carrier section
            if (recyclingOrder.LogisticType == 2) //Common Carrier
            {
                if(recyclingOrder.FreightCarriers != null)
                {
                    var freightCarrier = recyclingOrder.FreightCarriers.OrderBy(it => it.Id).FirstOrDefault() ?? new FreightCarrier();

                    model.Order.FreightCarrier = new RecyclingOrderFreightCarrierInfo
                    {
                        Carrier = freightCarrier.CarrierCustomer,
                        CarrierContact = freightCarrier.CarrierContact,
                        QuotedRate = freightCarrier.QuoteRate,
                        Price = freightCarrier.ClientCharge,
                        Insurance = freightCarrier.Insurance,
                        IsBlind = freightCarrier.IsBlindDropShipment
                    };

                    var carrierAddress = AddressService.GetAddress(freightCarrier.CarrierAddressId ?? 0);
                    model.Order.FreightCarrier.CarrierAddress = AddressMapper.Map(carrierAddress);
                }
                
            }
            // end filling InboundOrderInfo

            // Inbound order - references tab
            model.Reference = _mapper.Map<InboundOrderReferenceInfo>(recyclingOrder);

            // Inbound order - order detail tab
            var locationDetails = _inboundOrderService.GetRecyclingLocationDetails(orderId, recyclingOrderPdf.PickupAddressId);
            var orderEqipment = _inboundOrderService.GetRecyclingEquipment(orderId).ToList();

            // Location details and building logistics
            model.Details = _mapper.Map<InboundOrderDetails>(locationDetails) ?? new InboundOrderDetails();
            if (locationDetails != null)
            {
                model.Details.DockHeightRestrictions = locationDetails.IsDockHeightOver14 ? "Over 14'" : "Under 14'";
                model.Details.BuildingLogistics = string.Join(", ", locationDetails.FormatOptions());
            }
            model.Details.EquipmentNotes = recyclingOrder.EquipmentNotes;

            // Equipment required
            model.Details.RequiredEquipment = orderEqipment.Any() ?
                string.Join("; ", orderEqipment.Where(x => x.state).Select(x => x.label + (x.value > 1 ? $": {x.value}" : string.Empty)).ToArray())
                : string.Empty;

            // Certificates tab
            var certIds = RecyclingOrderDataService.GetRecyclingOrderInboundCertificateList(orderId)
                .Where(x => !x.IsInactive).Select(x => x.Id).ToArray();
            model.Certificates = certIds.Select(x =>
            {
                var cert = RecyclingOrderDataService.GetRecyclingOrderInboundCertificate(x ?? 0);
                return cert == null ? null : _mapper.Map<InboundOrderCertificateInfo>(cert);
            }).ToArray();

            // Inbound order lots
            var orderItems = RecyclingOrderDataService.GetRecyclingOrderItems(
                orderId,
                "RECYCLING_ORDER_ITEM_ID",
                "desc",
                ReportBuidlerConsts.AllAvailableRows, 
                0,
                "",
                out _);
            model.Lots = orderItems.Any() ? _mapper.Map<RecyclingOrderLotInfo[]>(orderItems) : null;

            // Services & Equipment
            model.Equipment = orderEqipment.Any() ? orderEqipment.Where(x => x.state).Select(x => new SimpleItem(x)).ToArray() : null;

            var services = _settlementDataService.GetSettlementServices(rq.RootEntityId);
            model.Services = _mapper.Map<SettlementService[]>(services);

            var commodities = RecyclingOrderDataService.GetCommoditiesForReport(orderId).ToArray();
            model.Commodities = commodities.Select(x => new InboundCommodityItemInfo(x)).ToArray();

            var serviceRequirements = RecyclingOrderDataService.GetServiceRequirements(rq.RootEntityId);
            model.ServiceRequirements = serviceRequirements.Select(x => new RecyclingOrderServiceRequirement(x)).ToArray();

            CurrencyConverter.EnumerableToForeign(model.Services);
            CurrencyConverter.EnumerableToForeign(model.Commodities);
            CurrencyConverter.EnumerableToForeign(model.ServiceRequirements);

            // Data destruction
            var assets = RecyclingOrderDataService.GetRecyclingOrderAssets(orderId).Select(x => 
                new AssetAuditModel()
                {
                    Id = x.Id,
                    Category = x.Category,
                    CategoryShort = x.CategoryShort,
                    CommodityCode = x.CommodityCode,
                    ItemNumber = x.ItemNumber,
                    SerialNumber = x.Serial,
                    UniqueId = x.UniqueId,
                    Notes = x.Notes,
                    Tag = x.AssetTag                    
                }).ToList();

            var dataDestructions = RecyclingOrderDataService.GetDataDestruction(assets.Select(it => it.Id).ToArray());
            var partHarvests = RecyclingOrderDataService.GetAssetNestingInfo(assets.Select(it => it.Id).ToArray());
            assets.ForEach(x =>
                {
                    var assetDataDestruction = dataDestructions.FirstOrDefault(c => c.AssetId == x.Id);
                    if (assetDataDestruction == null)
                        return;

                    var dd = assetDataDestruction.Drives.Where(y => y.AssetId == x.Id);
                    x.DriveDestruction = dd.Select(y => _mapper.Map<DriveDestructionInfo>(y)).ToArray();
                    x.DriveDestruction = x.DriveDestruction.Any() ? x.DriveDestruction : GetDriveDestructionStub();
                    Array.ForEach(x.DriveDestruction, c => c.DriveState = assetDataDestruction.DestructionState);

                    var ph = partHarvests.Where(y => y.RequestedAssetId == x.Id).Select(y => _mapper.Map<AssetAuditMainInfo>(y)).ToArray();
                    if (ph.Length > 0)
                    {
                        x.RemovedAssets = ph.Where(y => !y.IsParent).ToArray();
                        x.Parent        = ph.FirstOrDefault(y => y.IsParent);
                    }

                    if (x.RemovedAssets == null)
                    {
                        x.RemovedAssets = Array.Empty<AssetAuditMainInfo>();
                    }

                    if (x.Parent == null)
                    {
                        x.Parent = new AssetAuditMainInfo();
                    }
                }
            );

            model.Assets = assets.ToArray();
            // Received commodity summary
            var recCommdties = RecyclingOrderDataService.GetRecyclingOrderItemsForCertificateReport(orderId);
            model.ReceivedCommoditySummary = recCommdties.Any() ? _mapper.Map<InboundOrderCommoditySummary[]>(recCommdties) : null;
            var signatureInfos = _signatureShare.GetSignaturesTextInfo(orderId);

            model.Signatures = signatureInfos.Select(x => new SignatureModel
            {
                SignatureUrl = _signatureShare.GetSignatureWebFilePath(orderId, x.FileName),
                SignatureFullName = x.FullName,
                SignatureTitle = x.Title
            }).ToArray();
            
            // Asset purchase draft
            var draftDetail = RecyclingOrderDataService.GetInboundDraftAssetDetail(orderId);
            var draftAssets = RecyclingOrderDataService.GetInboundDraftAssetList(orderId);
            model.AssetPurchaseDraft = draftDetail;
            model.AssetPurchaseDraftItems = draftAssets.ToArray();

            model.Onsite = _onsiteReportService.GetOnsiteInfo(orderId);

            return model;
        }

        private static string LogisticIdToString(int id)
        {
            switch (id)
            {
                case 1: return "Our Trucking";
                case 2: return "Common Carrier";
                case 3: return "Client Drop Off";
                default: return "";
            }
        }
        private DriveDestructionInfo[] GetDriveDestructionStub() =>
            new []
            {
                new DriveDestructionInfo
                {
                    Drive = new DriveInfo()
                }
            };

        private readonly IInboundOrderService _inboundOrderService;
        private readonly ILocationDataService _locationDataService;
        protected readonly ISettlementDataService _settlementDataService;
        private readonly IContextVariablesProvider _context;
        private readonly ITempSignatureFileShare _signatureShare;
        private readonly IOnsiteReportService _onsiteReportService;

        private static readonly IMapper _mapper = new MapperConfiguration(cfg =>
        {
            // keep mappings complete when adding fields to models
            cfg.AddProfile(new RecyclingModelsMappingProfile());
            cfg.CreateMap<Data.Core.DataServices.RecyclingOrder.Models.RecyclingOrder, InboundOrderReferenceInfo>()
                .ForMember(dest => dest.Reference1, opt => opt.MapFrom(src => src.Reference1))
                .ForMember(dest => dest.Reference2, opt => opt.MapFrom(src => src.Reference2))
                .ForMember(dest => dest.Reference3, opt => opt.MapFrom(src => src.Reference3))
                .ForMember(dest => dest.Reference4, opt => opt.MapFrom(src => src.Reference4))
                .ForMember(dest => dest.Reference5, opt => opt.MapFrom(src => src.Reference5))
                .ForMember(dest => dest.Reference6, opt => opt.MapFrom(src => src.Reference6))
                .ForMember(dest => dest.Reference7, opt => opt.MapFrom(src => src.Reference7))
                .ForMember(dest => dest.Reference8, opt => opt.MapFrom(src => src.Reference8))
                .ForMember(dest => dest.SMEApprover, opt => opt.MapFrom(src => src.SMEApprover))
                .ForMember(dest => dest.CBREDisposalRequestWO, opt => opt.MapFrom(src => src.CBREDisposalRequestWO))
                .ForMember(dest => dest.DisposalRequestIDName, opt => opt.MapFrom(src => src.DisposalRequestIDName))
                .ForMember(dest => dest.DepartmentTurningEquipment, opt => opt.MapFrom(src => src.DepartmentTurningEquipment))
                .ForMember(dest => dest.CustomerContractNo, opt => opt.MapFrom(src => src.CustomerContractNo))
                ;
            
            cfg.CreateMap<RecyclingOrderLocationDetails, InboundOrderDetails>()
                .ForMember(dest => dest.EquipmentLocated, opt => opt.MapFrom(src => src.EquipmentLocated))
                .ForMember(dest => dest.BuildingLogistics, opt => opt.Ignore())
                .ForMember(dest => dest.TruckPark, opt => opt.MapFrom(src => src.TruckPark))
                .ForMember(dest => dest.LargeItems, opt => opt.MapFrom(src => src.LargeItemsInfo))
                .ForMember(dest => dest.DockHeightRestrictions, opt => opt.Ignore())
                .ForMember(dest => dest.RollingBinsToDropOff, opt => opt.MapFrom(src => src.RollingBinsToDropOff))
                .ForMember(dest => dest.RollingBinsToPickUp, opt => opt.MapFrom(src => src.RollingBinsToPickUp))
                .ForMember(dest => dest.BuildingNotes, opt => opt.MapFrom(src => src.BuidlingNotes))
                .ForMember(dest => dest.BolSpecialInstructions, opt => opt.MapFrom(src => src.BolSpecialInstructions))
                .ForMember(dest => dest.PickupProcessingNotes, opt => opt.MapFrom(src => src.PickupProcessingNotes))
                .ForMember(dest => dest.LocationOfMaterials, opt => opt.MapFrom(src => src.LocationOfMaterials))
                .ForMember(dest => dest.InventoryList, opt => opt.MapFrom(src => src.InventoryList))
                .ForMember(dest => dest.RequiredEquipment, opt => opt.Ignore())
                .ForMember(dest => dest.EquipmentNotes, opt => opt.Ignore())
                ;
            cfg.CreateMap<InboundBillOfLadingItem, 
                InboundOrderBillOfLandingItem>()
                .ForMember(dest => dest.ItemName, opt => opt.MapFrom(src => src.ItemName))
                .ForMember(dest => dest.Qty, opt => opt.MapFrom(src => src.Qty))
                .ForMember(dest => dest.PackagingType, opt => opt.MapFrom(src => src.PackagingType))
                .ForMember(dest => dest.HandlingType, opt => opt.MapFrom(src => src.HandlingType))
                .ForMember(dest => dest.ItemDescr, opt => opt.MapFrom(src => src.ItemDescr))
                ;
            cfg.CreateMap<RecyclingOrderInboundCertificateModel, InboundOrderCertificateInfo>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.AutoName))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Title))
                .ForMember(dest => dest.IssueDate, opt => opt.MapFrom(src => src.IssueDate))
                .ForMember(dest => dest.Text, opt => opt.MapFrom(src => src.Text))
                .ForMember(dest => dest.Summary, opt => opt.MapFrom(src => src.SummaryText))
                ;
            cfg.CreateMap<RecyclingCertificateReceivedItemReport, InboundOrderCommoditySummary>()
                .ForMember(dest => dest.Commodity, opt => opt.MapFrom(src => src.ItemTypeCd))
                .ForMember(dest => dest.AlternativeCommodityName, opt => opt.MapFrom(src => src.ItemTypeCd))
                .ForMember(dest => dest.Count, opt => opt.MapFrom(src => src.ItemCount))
                .ForMember(dest => dest.UnitCount, opt => opt.MapFrom(src => src.UnitCount))
                .ForMember(dest => dest.Gross, opt => opt.MapFrom(src => src.GrossWeight))
                .ForMember(dest => dest.Tare, opt => opt.MapFrom(src => src.TareWeight))
                .ForMember(dest => dest.Net, opt => opt.MapFrom(src => src.NetWeight))
                ;
            cfg.CreateMap<AssetAuditInfo, AssetAuditMainInfo>();
        }).CreateMapper();
    }
}
