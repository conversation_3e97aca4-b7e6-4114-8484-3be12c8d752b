using Razor.Cache.Redis.Base.CacheService.CacheOptionsSource;
using Razor.Cache.Redis.Configuration.Source;

namespace Razor.Cache.Redis.Services.InterUiRedirect.CacheOptionsSource
{
    public sealed class RedirectDataCacheOptionsSource : EntityCacheOptionsSourceBase, IRedirectDataCacheOptionsSource
    {
        public RedirectDataCacheOptionsSource(ICacheConfigSource config) : base(config)
        {
        }

        protected override string ConfigKey { get; } = "RedirectData";
    }
}