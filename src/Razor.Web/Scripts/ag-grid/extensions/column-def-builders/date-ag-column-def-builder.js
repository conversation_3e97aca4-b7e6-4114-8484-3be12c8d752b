function DateAgColumnDefBuilder(utc) {
    const it = this;
    it._buildingColumnDef = {
        filter: 'agDateColumnFilter',
        filterParams: {
            suppressAndOrCondition: true,
            newRowsAction: 'keep',
            buttons: ['clear'],
            browserDatePicker: true
        },
        valueFormatter: utc ? AgGridValueFormatters.dateUtc : AgGridValueFormatters.date
    };
}

DateAgColumnDefBuilder.prototype = Object.create(BaseAgColumnDefBuilder.prototype);
