using System.Collections.Generic;
using Razor.Common.Base.CommonModels.Pagination;
using Razor.Tools.DataAccess.DataServiceBase;

namespace Razor.Data.Core.DataServices.Tax
{
    public interface ITaxDataService : IBaseRepository
    {
        TaxGridItem GetTax(long taxId);
        PageOfRows<TaxGridItem> PageOfTaxes(IPageRequest request, string sqlFilter);
        IEnumerable<TaxGridItem> SyncTaxRates(IEnumerable<SyncTaxRateModel> taxes);
        TaxDynaTreeNode[] GetChildTaxGroup(long? parentId);
        TaxDynaTreeNode[] SearchTaxInTree(string text);
        TaxDynaTreeNode GetTaxTreeNode(long taxId);
    }
}