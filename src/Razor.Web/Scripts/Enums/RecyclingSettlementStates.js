RecyclingSettlementStates = {
    Initial: 1,
    Processed: 2,
    ProcessedAudit: 3,
    Audit: 4,
    InitialAndAudit: 5,
    getText: function (type) {
        switch (type) {
            case this.Initial:
                {
                    return "Initial";
                }
            case this.Processed:
                {
                    return "Processed";
                }
            case this.ProcessedAudit:
                {
                    return "Processed & Audit";
                }
            case this.Audit:
                {
                    return "Audit & ITad";
                }
            case this.InitialAndAudit:
                {
                    return "Initial & Audit";
                }
            default:
                {
                    throw new Error("Settlement state is not defined");
                }
        }
    }
};