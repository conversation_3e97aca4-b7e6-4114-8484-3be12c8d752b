using System;
using Razor.Common.Base.Attributes;

namespace Razor.Data.Core.DataServices.Report.Models.RecyclingInboundOrder
{
    public class RecyclingInboundOrder
    {
        [ColumnCaption(IsHidden = true)]
        [DbColumnMapping(SourceColumn = "RowID")]
        public Int32 RowId { set; get; }


        [ColumnCaption(Caption = "ReceivedDate", Order = 1)]
        [DbColumnMapping(SourceColumn = "ReceivedDate")]
        public DateTime? ReceivedDate { set; get; }

        [ColumnCaption(Caption = "PICKUP_DATE", Order = 2)]
        [DbColumnMapping(SourceColumn = "PICKUP_DATE")]
        public DateTime? PICKUPDATE { get; set; }

        [ColumnCaption(Caption = "FreightStatus", Order = 3)]
        [DbColumnMapping(SourceColumn = "FreightStatus")]
        public string FreightStatus { get; set; }

        [ColumnCaption(Caption = "WarehouseName", Order = 4)]
        [DbColumnMapping(SourceColumn = "WarehouseName")]
        public string WarehouseName { get; set; }

        [ColumnCaption(Caption = "RECYCLING_ORDER_ID", Order = 5)]
        [DbColumnMapping(SourceColumn = "RECYCLING_ORDER_ID")]
        public Int32? RECYCLING_ORDER_ID { get; set; }

        [ColumnCaption(Caption = "Original_Customer", Order = 6)]
        [DbColumnMapping(SourceColumn = "Original_Customer")]
        public string OriginalCustomer { get; set; }

        [ColumnCaption(Caption = "Pickup_Address", Order = 7)]
        [DbColumnMapping(SourceColumn = "Pickup_Address")]
        public string PickupAddress { get; set; }

        [ColumnCaption(Caption = "ReceiveConsumedDate", Order = 8)]
        [DbColumnMapping(SourceColumn = "ReceiveConsumedDate")]
        public DateTime? ReceiveConsumedDate { get; set; }

        [ColumnCaption(Caption = "Order_Status", Order = 9)]
        [DbColumnMapping(SourceColumn = "Order_Status")]
        public string Order_Status { get; set; }

        [ColumnCaption(Caption = "ParentLotID", Order = 10)]
        [DbColumnMapping(SourceColumn = "ParentLotID")]
        public string ParentLotID { get; set; }

        [ColumnCaption(Caption = "LotId", Order = 11)]
        [DbColumnMapping(SourceColumn = "LotId")]
        public string LotId { get; set; }

        [ColumnCaption(Caption = "Category", Order = 12)]
        [DbColumnMapping(SourceColumn = "Category")]
        public string Category { get; set; }

        [ColumnCaption(Caption = "Unit_Type", Order = 13)]
        [DbColumnMapping(SourceColumn = "Unit_Type")]
        public string UnitType { get; set; }

        [ColumnCaption(Caption = "Packaging_TYPE", Order = 14)]
        [DbColumnMapping(SourceColumn = "Packaging_TYPE")]
        public string Packaging_TYPE { get; set; }

        [ColumnCaption(Caption = "Weight", Order = 15)]
        [DbColumnMapping(SourceColumn = "Weight")]
        public Int32 Weight { get; set; }

        [ColumnCaption(Caption = "Tare", Order = 16)]
        [DbColumnMapping(SourceColumn = "Tare")]
        public string Tare { get; set; }


        [ColumnCaption(Caption = "Net", Order = 17)]
        [DbColumnMapping(SourceColumn = "Net")]
        public string Net { get; set; }

        [ColumnCaption(Caption = "Purchase_Price", Order = 18)]
        [DbColumnMapping(SourceColumn = "Purchase_Price")]
        public float? PurchasePrice { get; set; }

        [ColumnCaption(Caption = "PO", Order = 19)]
        [DbColumnMapping(SourceColumn = "PO")]
        public string PO { get; set; }

        [ColumnCaption(Caption = "Invoice", Order = 20)]
        [DbColumnMapping(SourceColumn = "Invoice")]
        public string Invoice { get; set; }

        [ColumnCaption(Caption = "Settled_Date", Order = 21)]
        [DbColumnMapping(SourceColumn = "Settled_Date")]
        public DateTime? SettledDate { get; set; }

        [ColumnCaption(Caption = "SettlementType", Order = 22)]
        [DbColumnMapping(SourceColumn = "SettlementType")]
        public string SettlementType { get; set; }

        [ColumnCaption(Caption = "Notes", Order = 23)]
        [DbColumnMapping(SourceColumn = "Notes")]
        public string Notes { get; set; }

        [ColumnCaption(Caption = "Workflow", Order = 24)]
        [DbColumnMapping(SourceColumn = "Workflow")]
        public string Workflow { get; set; }

        [ColumnCaption(Caption = "ConsumedDate", Order = 25)]
        [DbColumnMapping(SourceColumn = "ConsumedDate")]
        public DateTime? ConsumedDate { get; set; }

        [ColumnCaption(Caption = "REP", Order = 26)]
        [DbColumnMapping(SourceColumn = "REP")]
        public string REP { get; set; }

        [ColumnCaption(Caption = "Outbound_Order", Order = 27)]
        [DbColumnMapping(SourceColumn = "Outbound_Order")]
        public Int32 OutboundOrder { get; set; }

        [ColumnCaption(Caption = "Outgoing_Customer", Order = 28)]
        [DbColumnMapping(SourceColumn = "Outgoing_Customer")]
        public string OutgoingCustomer { get; set; }

        [ColumnCaption(Caption = "Selling_Price", Order = 29)]
        [DbColumnMapping(SourceColumn = "Selling_Price")]
        public float? SellingPrice { get; set; }

        [ColumnCaption(Caption = "SO_#", Order = 30)]
        [DbColumnMapping(SourceColumn = "SO_#")]
        public Int32? SO { get; set; }

    }
}
