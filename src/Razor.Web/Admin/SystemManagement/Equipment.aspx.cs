using System;
using Razor.Common.Base.Context.Models;
using Razor.Common.Core.UserAccess.Enums;
using RazorWeb.Baseclass;


namespace RazorWeb.Admin.SystemManagement
{
    public partial class Equipment : SecuredBasePage
    {
        public Equipment()
        {
            ProtectionLevel = SystemAccessLevels.Admin;
            FunctionalSectionId = FunctionalSections.Undefined;
            AdminPageId = SettingsPages.Equipment;
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            base.OnInit(e);
            this.HighlightMenuItem(new[] { FunctionalSections.GrSystem }, FunctionalSectionId);
        }
    }
}