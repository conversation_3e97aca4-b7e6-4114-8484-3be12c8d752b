using System;
using Razor.Common.Base.Attributes;

namespace Razor.Data.Core.DataServices.PickList.Models
{
    public class PickListMatchingItemInventoryModel
    {
        public Guid? LineId { set; get; }

        public Guid? ForLineId { set; get; }

        public long ItemInventoryId { set; get; }

        [ColumnCaption(Caption = "Quoted", Order = 1)]
        public int Quoted { set; get; }

        [ColumnCaption(Caption = "Condition", Order = 2)]
        public string Condition { set; get; }

        [ColumnCaption(Caption = "Warehouse", Order = 3)]
        public string Warehouse { set; get; }

        [ColumnCaption(Caption = "Model", Order = 4)]
        public string PartNumber { set; get; }

        [ColumnCaption(Caption = "Manufacturer", Order = 5)]
        public string Manufacturer { set; get; }

        [ColumnCaption(Caption = "SKU", Order = 6)]
        public string Sku { set; get; }

        [ColumnCaption(Caption = "UID", Order = 7)]
        public string UniqueId { set; get; }

        [ColumnCaption(Caption = "Serial", Order = 8)]
        public string Serial { set; get; }

        [ColumnCaption(Caption = "Item Qty", Order = 9)]
        public int ItemQty { set; get; }

        [ColumnCaption(Caption = "Location", Order = 10)]
        public string Location { set; get; }

        [ColumnCaption(Caption = "Notes", Order = 11)]
        public string Notes { set; get; }

        [ColumnCaption(Caption = "Heci", Order = 12)]
        public string Heci { set; get; }

        [ColumnCaption(Caption = "Revision", Order = 13)]
        public string Revision { set; get; }

        [ColumnCaption(Caption = "Status", Order = 14)]
        public string Status { set; get; }

        [ColumnCaption(Caption = "Age (days)", Order = 15)]
        public int AgeDays { set; get; }

        [ColumnCaption(Caption = "Account", Order = 16)]
        public string CustomerName { set; get; }

        [ColumnCaption(Caption = "Description", Order = 17)]
        public string Description { set; get; }
    }
}
