using System;
using Razor.Common.Base.Attributes;

namespace Razor.Data.Core.DataServices.Remote.Printing.Models
{
    public class PrinterJob : PrinterParams
    {
        [DbColumnMapping(SourceColumn = "PRINTER_JOB_ID")]
        public Int64 JobId { get; set; }
        [DbColumnMapping(SourceColumn = "PRINT_DATA")]
        public String JobData { get; set; }
        [DbColumnMapping(SourceColumn = "PRINTER_JOB_TYPE_ID")]
        public Int32 JobType { get; set; }
        [DbColumnMapping(SourceColumn = "FILE_NAME")]
        public String FileName { get; set; }
    }
}