using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Razor.Common.Web.JqGrid.FilterParser.Parser.Model.Filter
{
    [DataContract]
    public class Filter
    {
        public static Filter GetFilter(GroupOperation operation = GroupOperation.Or, string name = "")
        {
            return new Filter
            {
                GroupOperation = operation,
                Groups = new List<Filter>(),
                Rules = new List<Rule>(),
                Name = name
            };
        }

        public static Filter AddRuleToFilterObject(Rule[] rules, Filter filterObj = null, bool startNewGroup = true, string name = "", string parentName = "", GroupOperation operation = GroupOperation.And)
        {
            bool isNested = !String.IsNullOrEmpty(parentName);

            if (filterObj == null)
            {
                filterObj = GetFilter(operation, name);
            }           
            if (startNewGroup && !isNested || filterObj.Groups.Count == 0)
            {
                filterObj.Groups.Add(GetFilter(operation, name));
            }

            Filter group = filterObj.Groups[filterObj.Groups.Count - 1];
            if (isNested)
            {
                var groups = filterObj.Groups.SelectManyRecursive(g => g.Groups);
                var result = groups.FirstOrDefault(n => n.Name == parentName);
                if (result != null)
                {
                    group = result;
                }
            }
                       
            if (startNewGroup && isNested)
            {
                Filter newGroup = GetFilter(operation, name);
                group.Groups.Add(newGroup);
                group = newGroup;
            }

            group.Rules.AddRange(rules);           
                        
            return filterObj;
        }

        [DataMember(Name = "groupOp")]
        [JsonProperty(ItemConverterType = typeof(StringEnumConverter))]
        public GroupOperation GroupOperation { get; set; }

        [DataMember(Name = "rules")]
        public List<Rule> Rules { get; set; }

        [DataMember(Name = "groups")]
        public List<Filter> Groups { get; set; }

        public string Name { get; set; }
    }
}