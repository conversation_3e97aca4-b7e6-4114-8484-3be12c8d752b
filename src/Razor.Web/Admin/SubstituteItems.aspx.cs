using System;
using System.Collections.Generic;
using System.Linq;
using Razor.Common.Base.Helpers;
using Razor.Common.Core.UserAccess.Enums;
using Razor.Common.Web.JqGrid;
using Razor.Data.Core;
using Razor.Data.LegacyTableAdapters.DataAdapters.RZR_ERPTableAdapters;
using Razor.Data.Core.DataServices.Inventory.Models.InventoryItemDetails;
using Razor.Data.Core.Enums;
using Razor.Logic.Common;
using Razor.Tools.DataAccess;
using Razor.Tools.DataAccess.Helpers;
using RazorWeb.Baseclass;
using Razor.Common.Web.JqGrid.FilterParser;
using JqGridRequest = RazorWeb.DataModels.JqGrid.Base.JqGridRequest;
using CommonServiceLocator;
using Razor.Common.Web.WebHelpers;

namespace RazorWeb.Admin
{
    public partial class SubstituteItems : SecuredBasePage
    {
        public SubstituteItems()
        {
            FunctionalSectionId = FunctionalSections.GrInventory;
        }
        
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Request.QueryString.Count != 1 || Request.QueryString.Keys[0] != "Params")
            {
                ProcessGridRequest();
            }
        }   

        private void ProcessGridRequest()
        {
            String actionPage = Request["ActionPage"];
            if (actionPage != "TransportType")
            {
                return;
            }

            String action = Request["Action"];
            switch (action)
            {
                case "Fill":
                    Int32 rows;
                    if (!Int32.TryParse(Request["rows"], out rows))
                    {
                        rows = 10;
                    }
                    Int32 page;
                    if (!Int32.TryParse(Request["page"], out page))
                    {
                        page = 1;
                    }


                    var gridParams = new JqGridRequest
                    {
                        rows = rows,
                        page = page,
                        sidx = Request["sidx"],
                        sord = Request["sord"],
                        filters = Request["filters"]
                    };

                    String itemId = Request["ItemId"];
                    String masterItemId = Request["ItemMasterId"];
                    Int64 skuId;
                    Int64 masterId;
                    if (Int64.TryParse(itemId, out skuId) && Int64.TryParse(masterItemId, out masterId))
                    {
                        String output = BuildJqGridResults(gridParams, skuId, masterId);
                        Response.Cache.SetMaxAge(new TimeSpan(0));
                        Response.ContentType = "application/json";
                        Response.Write(output);
                        Context.CloseHttpResponse();
                    }
                    break;
            }
        }

        private String BuildJqGridResults(JqGridRequest request, Int64 skuId, Int64 masterId)
        {
            var parser = ServiceLocator.Current.GetInstance<FilterParser>();
            using (var ta = TableAdapterFactory.CreateAdapter<sp_GET_ITEM_INVENTORYTableAdapter>(ConnectionsStub.Company(ContextVariablesProvider)))
            using (var dt = ta.GetData(skuId
                                        , masterId
                                        , DbMappingHelper.GetColumnName<SubstituteItemDetails>(request.sidx)
                                        , request.sord
                                        , request.rows
                                        , request.PageIndex0Based
                                        , parser.FilterStringToSql<SubstituteItemDetails>(request)))
            {

                Int32 totalRecords = dt.Rows.Count > 0 && dt.Rows[0].ItemArray.Length > 0
                                         ? Convert.ToInt32(dt.Rows[0][1])
                                         : 0;
                List<SubstituteItemDetails> pageData = dt.MapTable<SubstituteItemDetails>(1)
                    .Distinct()
                    .ToList();

                var totalPages = (Int32) Math.Ceiling(totalRecords/(Double) request.rows);
                if (totalPages == 0 && totalRecords > 0)
                {
                    totalPages = 1;
                }

                var result = new DataModels.JqGrid.Base.JqGridResult<SubstituteItemDetails>
                    {
                        page = request.page,
                        total = totalPages,
                        records = totalRecords,
                        rows = pageData,
                        rowNum = request.rows
                    };

                return result.SerializeToJson();
            }

        }
    }
}