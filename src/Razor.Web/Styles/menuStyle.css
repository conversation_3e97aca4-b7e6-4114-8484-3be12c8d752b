.TopGroup
{
  background-color:#000;
  background-image: url(../images/top_groupBg.gif);
  border:1px solid #000;
  cursor:default;
}

.MenuGroup {
  background-color:#f6f6f6;
  border:1px solid #666;
  cursor:default;
}

.TopMenuItem
{
  color:white;
  font-family:tahoma;
  font-size:12px;
  font-weight: bold;
  padding:20px;
  cursor:default;
}

.TopMenuItemHover
{
  color:white;
  background-image: url(../images/top_itemHoverBg.gif);
  background-color:#b9b9b9;
  font-family:tahoma;
  font-size:12px;
  font-weight: bold;
  cursor:default;
}

.MenuItem {
  border: 1px solid #f6f6f6;
  color:#666;
  font-family:tahoma;
  font-size:11px;
  cursor:default;
}

.MenuItemHover {
  margin:0;
  background:#fff url("../images/itemHoverBg.gif") repeat-x;
  color: #000;
  border: 1px solid #ccc;
  border-right-color:#b9b9b9;
  border-bottom-color:#b9b9b9;
  font-family:tahoma;
  font-size:11px;
  cursor:default;
}

.MenuItemExpanded
{
  margin:0;
  background:#fff url("../images/itemHoverBg.gif") repeat-x;
  color: #000;
  border: 1px solid #ccc;
  border-right-color:#b9b9b9;
  border-bottom-color:#b9b9b9;
  font-family:tahoma;
  font-size:11px;
  cursor:default;
}

.MenuItemActive
{
  margin:0;
  background:#fff url("../images/itemActiveBg.gif") repeat-x;
  color: #454545;
  border: 1px solid #ddd;
  border-right-color:#ccc;
  border-bottom-color:#ccc;
  font-family:tahoma;
  font-size:11px;
  cursor:default;
}

.MenuBreak
{
  background-color: silver;
  width:100%;
  height:2px;
}


.SetMarginsHover {
  margin:0;
  background:#fff url("../images/setMarginsHoverBg.gif") repeat-x;
  color: #000;
  border: 1px solid #ccc;
  border-right-color:#b9b9b9;
  border-bottom-color:#b9b9b9;
  font-family:tahoma;
  font-size:11px;
  cursor:default;
}


.SetMarginsActive {
  margin:0;
  background:#fff url("../images/setMarginsActiveBg.gif") repeat-x;
  color: #454545;
  border: 1px solid #ddd;
  border-right-color:#ccc;
  border-bottom-color:#ccc;
  font-family:tahoma;
  font-size:11px;
  cursor:default;
}


.ScrollItem
{
  background-color:#eee;
  color:#666;
  border: 1px solid #ccc;
  text-align:center;
  cursor:default;
}

.ScrollItemH
{
  background-color:#eee;
  color:#000;
  border: 1px solid #ccc;
  border-right-color:#b9b9b9;
  border-bottom-color:#b9b9b9;
  text-align:center;
  cursor:default;
}

.ScrollItemA
{
  color: #454545;
  background-color:#fff;
  border: 1px solid #ddd;
  border-right-color:#ccc;
  border-bottom-color:#ccc;
  text-align:center;
  cursor:default;
}

