using System;
using System.Collections.Generic;

namespace Razor.Mq.Contracts.Geocoding.GetCoordinates.Payloads
{
    public class GeocodingAddressDto : IEquatable<GeocodingAddressDto>
    {
        public string City { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }
        public string Street { get; set; }
        public string CountryCode { get; set; }

        #region IEquatable
        public override bool Equals(object obj) => Equals(obj as GeocodingAddressDto);
        public bool Equals(GeocodingAddressDto other) => 
            !(other is null) && 
            City == other.City && 
            State == other.State &&
            PostalCode == other.PostalCode && 
            Street == other.Street && 
            CountryCode == other.CountryCode;

        public override int GetHashCode()
        {
            var hashCode = -938808587;
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(City);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(State);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(PostalCode);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(Street);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(CountryCode);
            return hashCode;
        }

        public static bool operator ==(GeocodingAddressDto left, GeocodingAddressDto right) => EqualityComparer<GeocodingAddressDto>.Default.Equals(left, right);
        public static bool operator !=(GeocodingAddressDto left, GeocodingAddressDto right) => !(left == right);
        #endregion IEquatable
    }
}
