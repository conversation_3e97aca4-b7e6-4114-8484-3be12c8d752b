//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace RemotePrinterService.RemotePrinter {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    using System.Drawing.Printing;
    using System.Drawing;


    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.3752.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="RemotePrinterServiceSoap", Namespace="http://tempuri.org/")]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PrinterParams))]
    public partial class RemotePrinterService : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback SetPrintersResolutionOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetPrintersNeedResolutionOperationCompleted;
        
        private System.Threading.SendOrPostCallback SetAvailablePrintersOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetPrinterJobsOperationCompleted;
        
        private System.Threading.SendOrPostCallback SetJobCompleteOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public RemotePrinterService() {
            this.Url = global::RemotePrinterService.Properties.Settings.Default.RemotePrinterService_RemotePrinter_RemotePrinterService;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event SetPrintersResolutionCompletedEventHandler SetPrintersResolutionCompleted;
        
        /// <remarks/>
        public event GetPrintersNeedResolutionCompletedEventHandler GetPrintersNeedResolutionCompleted;
        
        /// <remarks/>
        public event SetAvailablePrintersCompletedEventHandler SetAvailablePrintersCompleted;
        
        /// <remarks/>
        public event GetPrinterJobsCompletedEventHandler GetPrinterJobsCompleted;
        
        /// <remarks/>
        public event SetJobCompleteCompletedEventHandler SetJobCompleteCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/SetPrintersResolution", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void SetPrintersResolution(PrinterResolutionInfo[] resolutions, string[] notFound, string host) {
            this.Invoke("SetPrintersResolution", new object[] {
                        resolutions,
                        notFound,
                        host});
        }
        
        /// <remarks/>
        public void SetPrintersResolutionAsync(PrinterResolutionInfo[] resolutions, string[] notFound, string host) {
            this.SetPrintersResolutionAsync(resolutions, notFound, host, null);
        }
        
        /// <remarks/>
        public void SetPrintersResolutionAsync(PrinterResolutionInfo[] resolutions, string[] notFound, string host, object userState) {
            if ((this.SetPrintersResolutionOperationCompleted == null)) {
                this.SetPrintersResolutionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSetPrintersResolutionOperationCompleted);
            }
            this.InvokeAsync("SetPrintersResolution", new object[] {
                        resolutions,
                        notFound,
                        host}, this.SetPrintersResolutionOperationCompleted, userState);
        }
        
        private void OnSetPrintersResolutionOperationCompleted(object arg) {
            if ((this.SetPrintersResolutionCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SetPrintersResolutionCompleted(this, new System.ComponentModel.AsyncCompletedEventArgs(invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetPrintersNeedResolution", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string[] GetPrintersNeedResolution(string host) {
            object[] results = this.Invoke("GetPrintersNeedResolution", new object[] {
                        host});
            return ((string[])(results[0]));
        }
        
        /// <remarks/>
        public void GetPrintersNeedResolutionAsync(string host) {
            this.GetPrintersNeedResolutionAsync(host, null);
        }
        
        /// <remarks/>
        public void GetPrintersNeedResolutionAsync(string host, object userState) {
            if ((this.GetPrintersNeedResolutionOperationCompleted == null)) {
                this.GetPrintersNeedResolutionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetPrintersNeedResolutionOperationCompleted);
            }
            this.InvokeAsync("GetPrintersNeedResolution", new object[] {
                        host}, this.GetPrintersNeedResolutionOperationCompleted, userState);
        }
        
        private void OnGetPrintersNeedResolutionOperationCompleted(object arg) {
            if ((this.GetPrintersNeedResolutionCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetPrintersNeedResolutionCompleted(this, new GetPrintersNeedResolutionCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/SetAvailablePrinters", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void SetAvailablePrinters(string[] printerNames, string host) {
            this.Invoke("SetAvailablePrinters", new object[] {
                        printerNames,
                        host});
        }
        
        /// <remarks/>
        public void SetAvailablePrintersAsync(string[] printerNames, string host) {
            this.SetAvailablePrintersAsync(printerNames, host, null);
        }
        
        /// <remarks/>
        public void SetAvailablePrintersAsync(string[] printerNames, string host, object userState) {
            if ((this.SetAvailablePrintersOperationCompleted == null)) {
                this.SetAvailablePrintersOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSetAvailablePrintersOperationCompleted);
            }
            this.InvokeAsync("SetAvailablePrinters", new object[] {
                        printerNames,
                        host}, this.SetAvailablePrintersOperationCompleted, userState);
        }
        
        private void OnSetAvailablePrintersOperationCompleted(object arg) {
            if ((this.SetAvailablePrintersCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SetAvailablePrintersCompleted(this, new System.ComponentModel.AsyncCompletedEventArgs(invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetPrinterJobs", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public PrinterJob[] GetPrinterJobs(string host) {
            object[] results = this.Invoke("GetPrinterJobs", new object[] {
                        host});
            return ((PrinterJob[])(results[0]));
        }
        
        /// <remarks/>
        public void GetPrinterJobsAsync(string host) {
            this.GetPrinterJobsAsync(host, null);
        }
        
        /// <remarks/>
        public void GetPrinterJobsAsync(string host, object userState) {
            if ((this.GetPrinterJobsOperationCompleted == null)) {
                this.GetPrinterJobsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetPrinterJobsOperationCompleted);
            }
            this.InvokeAsync("GetPrinterJobs", new object[] {
                        host}, this.GetPrinterJobsOperationCompleted, userState);
        }
        
        private void OnGetPrinterJobsOperationCompleted(object arg) {
            if ((this.GetPrinterJobsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetPrinterJobsCompleted(this, new GetPrinterJobsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/SetJobComplete", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool SetJobComplete(PrinterJobCompleteModel[] items) {
            object[] results = this.Invoke("SetJobComplete", new object[] {
                        items});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void SetJobCompleteAsync(PrinterJobCompleteModel[] items) {
            this.SetJobCompleteAsync(items, null);
        }
        
        /// <remarks/>
        public void SetJobCompleteAsync(PrinterJobCompleteModel[] items, object userState) {
            if ((this.SetJobCompleteOperationCompleted == null)) {
                this.SetJobCompleteOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSetJobCompleteOperationCompleted);
            }
            this.InvokeAsync("SetJobComplete", new object[] {
                        items}, this.SetJobCompleteOperationCompleted, userState);
        }
        
        private void OnSetJobCompleteOperationCompleted(object arg) {
            if ((this.SetJobCompleteCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SetJobCompleteCompleted(this, new SetJobCompleteCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.3752.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PrinterResolutionInfo {
        
        private string printerNameField;
        
        private RectangleF printableAreaField;
        
        private PrinterResolution[] availableResolutionsField;
        
        private PaperSize[] paperSizesField;
        
        /// <remarks/>
        public string PrinterName {
            get {
                return this.printerNameField;
            }
            set {
                this.printerNameField = value;
            }
        }
        
        /// <remarks/>
        public RectangleF PrintableArea {
            get {
                return this.printableAreaField;
            }
            set {
                this.printableAreaField = value;
            }
        }
        
        /// <remarks/>
        public PrinterResolution[] AvailableResolutions {
            get {
                return this.availableResolutionsField;
            }
            set {
                this.availableResolutionsField = value;
            }
        }
        
        /// <remarks/>
        public PaperSize[] PaperSizes {
            get {
                return this.paperSizesField;
            }
            set {
                this.paperSizesField = value;
            }
        }
    }
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.3752.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PrinterJobCompleteModel {
        
        private long jobIdField;
        
        private string errorField;
        
        /// <remarks/>
        public long JobId {
            get {
                return this.jobIdField;
            }
            set {
                this.jobIdField = value;
            }
        }
        
        /// <remarks/>
        public string Error {
            get {
                return this.errorField;
            }
            set {
                this.errorField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PrinterJob))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.3752.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PrinterParams {
        
        private string printerIdField;
        
        private string printerIpField;
        
        private int printerPortField;
        
        /// <remarks/>
        public string PrinterId {
            get {
                return this.printerIdField;
            }
            set {
                this.printerIdField = value;
            }
        }
        
        /// <remarks/>
        public string PrinterIp {
            get {
                return this.printerIpField;
            }
            set {
                this.printerIpField = value;
            }
        }
        
        /// <remarks/>
        public int PrinterPort {
            get {
                return this.printerPortField;
            }
            set {
                this.printerPortField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.3752.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PrinterJob : PrinterParams {
        
        private long jobIdField;
        
        private string jobDataField;
        
        private int jobTypeField;
        
        private string fileNameField;
        
        /// <remarks/>
        public long JobId {
            get {
                return this.jobIdField;
            }
            set {
                this.jobIdField = value;
            }
        }
        
        /// <remarks/>
        public string JobData {
            get {
                return this.jobDataField;
            }
            set {
                this.jobDataField = value;
            }
        }
        
        /// <remarks/>
        public int JobType {
            get {
                return this.jobTypeField;
            }
            set {
                this.jobTypeField = value;
            }
        }
        
        /// <remarks/>
        public string FileName {
            get {
                return this.fileNameField;
            }
            set {
                this.fileNameField = value;
            }
        }
    }
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.3752.0")]
    public delegate void SetPrintersResolutionCompletedEventHandler(object sender, System.ComponentModel.AsyncCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.3752.0")]
    public delegate void GetPrintersNeedResolutionCompletedEventHandler(object sender, GetPrintersNeedResolutionCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.3752.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetPrintersNeedResolutionCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetPrintersNeedResolutionCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.3752.0")]
    public delegate void SetAvailablePrintersCompletedEventHandler(object sender, System.ComponentModel.AsyncCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.3752.0")]
    public delegate void GetPrinterJobsCompletedEventHandler(object sender, GetPrinterJobsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.3752.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetPrinterJobsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetPrinterJobsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public PrinterJob[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((PrinterJob[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.3752.0")]
    public delegate void SetJobCompleteCompletedEventHandler(object sender, SetJobCompleteCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.3752.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SetJobCompleteCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SetJobCompleteCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591