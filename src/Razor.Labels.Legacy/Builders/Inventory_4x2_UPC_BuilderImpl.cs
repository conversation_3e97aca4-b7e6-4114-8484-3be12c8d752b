using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;

using Razor.Labels.Models;

using System.Drawing.Drawing2D;
using Razor.Common.Core.DateTimeFormat;
using Razor.Labels.Builders.Image;
using Razor.Labels.Enums;
using Razor.Tools.Barcodes.Encoder;
using Razor.Labels.Extensions;
using Razor.Labels.Models.Inventory;

namespace Razor.Labels.Legacy.Builders
{
    public class Inventory_4x2_UPC_BuilderImpl : LabelBuilderBase<InventoryFullLabelModel>, IInventory_4x2_UPC_Builder<LegacyLabel>
    {
        protected override IEnumerable<Bitmap> BuildBitmaps(InventoryFullLabelModel model, DrawingSettings drawingSettings)
        {
            var outputBitMap = new Bitmap(drawingSettings.PxDimensions.X, drawingSettings.PxDimensions.Y);
            outputBitMap.SetResolution(PrinterResolutionX, PrinterResolutionY);

            using (Graphics gr = Graphics.FromImage(outputBitMap))
            {
                gr.Clear(drawingSettings.BackColor);
                gr.SmoothingMode = SmoothingMode.HighQuality;
                gr.InterpolationMode = InterpolationMode.HighQualityBicubic;
                gr.PixelOffsetMode = PixelOffsetMode.HighQuality;
                gr.CompositingQuality = CompositingQuality.HighQuality;

                Single fsNormal = drawingSettings.InchDimensions.X / 26f;
                using (var fontBold = new Font("Arial", ConvertInchesToPixels(fsNormal, _printerResolutionX), FontStyle.Bold, GraphicsUnit.Pixel))
                using (var fontNormal = new Font("Arial", ConvertInchesToPixels(fsNormal, _printerResolutionX), FontStyle.Regular, GraphicsUnit.Pixel))
                using (var fontLittle = new Font("Arial", ConvertInchesToPixels(fsNormal / 3 * 2, _printerResolutionX), FontStyle.Regular, GraphicsUnit.Pixel))
                using (var b = new BarcodeEncoder(gr, new PointF()) { Alignment = AlignmentPositions.LEFT })
                {
                    var normalTxtHt = gr.MeasureString("TXT", fontNormal).Height;
                    var barHeight = drawingSettings.PxDimensions.Y / 10;
                    var smallBarWidth = drawingSettings.PxDimensions.X / 4;
                    var corrections = new
                    {
                        AfterText = 20,
                        BeforeBarX = 20,
                        BarWidthDecrease = 40
                    };

                    var c = new CanvasCaret
                    {
                        Caret = new PointF(drawingSettings.PxMarginX, drawingSettings.PxMarginX),
                        Brush = drawingSettings.TextBrush,
                        Graphics = gr,
                        PxDimensions = drawingSettings.PxDimensions
                    };

                    // LEFT
                    // Descr must be moved to the new line if it is too long
                    var rect = new RectangleF(c.Caret, new SizeF(drawingSettings.PxDimensions.X - drawingSettings.PxMarginX * 2, 2.5F * normalTxtHt));
                    gr.DrawString(model.ModelTitle, fontNormal, c.Brush, rect);
                    c.Caret = new PointF(c.Caret.X, c.Caret.Y + normalTxtHt * 2);

                    // Part and Condition
                    AppendText(model.Condition?.ToUpper(), fontBold, c, corrections.AfterText, true);
                    AppendText(model.Model, fontNormal, c, corrections.AfterText, true);

                    // Attributes
                    var txt = model.Capabilities.GetCapabilityValues(new CapabilityLabelExtension.FixedCapabilityTypes[][]
                    {
                        new[] { CapabilityLabelExtension.FixedCapabilityTypes.Color },
                        new[] { CapabilityLabelExtension.FixedCapabilityTypes.MemorySize, CapabilityLabelExtension.FixedCapabilityTypes.Memory },
                        new[] { CapabilityLabelExtension.FixedCapabilityTypes.HDSize },
                        new[] { CapabilityLabelExtension.FixedCapabilityTypes.CPUSpeed },
                        new[] { CapabilityLabelExtension.FixedCapabilityTypes.CPUType },
                    });

                    var littleTxtHt = gr.MeasureString("TXT", fontLittle).Height;
                    var attrsWd = gr.MeasureString(txt, fontLittle).Width;
                    c.Caret = new PointF(drawingSettings.PxMarginX, c.Caret.Y);
                    if (attrsWd >= drawingSettings.PxDimensions.X - drawingSettings.PxMarginX * 2)
                    {
                        c.Caret = new PointF(c.Caret.X, c.Caret.Y - littleTxtHt / 8);
                    }
                    else
                    {
                        c.Caret = new PointF(c.Caret.X, c.Caret.Y + littleTxtHt / 4);
                    }
                    rect = new RectangleF(c.Caret, new SizeF(drawingSettings.PxDimensions.X - drawingSettings.PxMarginX * 2, 2.5F * littleTxtHt));
                    gr.DrawString(txt, fontLittle, c.Brush, rect);
                    c.Caret = new PointF(c.Caret.X, c.Caret.Y + normalTxtHt * 2F);
                    if (attrsWd >= drawingSettings.PxDimensions.X - drawingSettings.PxMarginX * 2)
                    {
                        c.Caret = new PointF(c.Caret.X, c.Caret.Y - littleTxtHt / 8);
                    }
                    else
                    {
                        c.Caret = new PointF(c.Caret.X, c.Caret.Y + littleTxtHt / 4);
                    }

                    // Serial
                    c.Caret = new PointF(drawingSettings.PxMarginX, c.Caret.Y - normalTxtHt + corrections.AfterText);
                    var number = String.IsNullOrEmpty(model.Serial) ? model.UniqueId : model.Serial;
                    b.Offset = new PointF(c.Caret.X + corrections.BeforeBarX, c.Caret.Y);
                    try
                    {
                        b.Encode(BarcodeFormats.CODE39Extended,
                            number,
                            drawingSettings.PxDimensions.X / 3 * 2,
                            barHeight);
                    }
                    catch (Exception)
                    { 
                    }
                    c.Caret = new PointF(c.Caret.X, c.Caret.Y + barHeight);
                    AppendText(number, fontNormal, c, corrections.AfterText, true);

                    // UPC code
                    var gradeY = c.Caret.Y;
                    if (!String.IsNullOrWhiteSpace(model.ProductCode))
                    {
                        var txtWd = gr.MeasureString("8", fontLittle).Width;
                        String codeText = model.ProductCode;

                        // output if a valid UPC
                        Int64 upcAsInt;
                        if (Int64.TryParse(model.ProductCode, out upcAsInt))
                        {
                            // barcode
                            b.Offset = new PointF(c.Caret.X + corrections.BeforeBarX, c.Caret.Y);
                            try
                            {
                                b.Encode(BarcodeFormats.UPCA,
                                    model.ProductCode,
                                    smallBarWidth,
                                    barHeight * 3 / 2);
                            }
                            catch (Exception)
                            {
                                //invalid UPC code
                                var caret = (CanvasCaret)c.Clone();
                                AppendText("EUPCA-1: Invalid code", fontLittle, c, corrections.AfterText, true);
                                c = caret;
                            }
                            // divide text by groups
                            if (model.ProductCode.Length > 7)
                            {
                                codeText = String.Format("{0}   {1} {2}   {3}",
                                    model.ProductCode[0],
                                    model.ProductCode.Substring(1, 5),
                                    model.ProductCode.Substring(6, model.ProductCode.Length - 7),
                                    model.ProductCode[model.ProductCode.Length - 1]);
                            }
                        }
                        c.Caret = new PointF(c.Caret.X + txtWd * 0.6F, c.Caret.Y + barHeight * 3 / 2);

                        AppendText(codeText, fontLittle, c, corrections.AfterText, true);
                    }

                    // Grade
                    var grade = "";// label.AttributeValues.GetValueOfAttribute(FixedCapabilityTypes.Grade);
                    if (!String.IsNullOrEmpty(grade))
                    {
                        c.Caret = new PointF(c.Caret.X,  gradeY);
                        c.PxDimensions = new Point(c.PxDimensions.X - drawingSettings.PxMarginX + corrections.BeforeBarX, c.PxDimensions.Y);
                        using (var fontBig = new Font("Arial", ConvertInchesToPixels(fsNormal * 2, _printerResolutionX), FontStyle.Regular, GraphicsUnit.Pixel))
                        {
                            AppendText(grade, fontBig, c, corrections.AfterText, false);
                        }
                    }
                }


                gr.Flush();

                return new Bitmap[] { outputBitMap };
            }
        }

        protected override LabelTypes _labelType => LabelTypes.Size_4x2_Inventory_UPC;

        public Inventory_4x2_UPC_BuilderImpl(IUserDateTimeFormatService userDateTimeFormatService) : base(userDateTimeFormatService)
        {
        }
    }
}
