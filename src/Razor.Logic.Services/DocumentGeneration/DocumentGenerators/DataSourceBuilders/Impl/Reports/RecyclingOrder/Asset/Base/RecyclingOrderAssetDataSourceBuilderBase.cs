using Razor.Common.Base.Context;
using Razor.Common.Base.Context.Data;
using Razor.Common.Core.Company.Data;
using Razor.Data.Core.DataServices.Customer;
using Razor.Data.Core.DataServices.User;
using Razor.Logic.Common.Helpers;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Impl.Reports.Base;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Mappers;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Services.CrmAccount;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.Models.Base;

namespace Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Impl.Reports.RecyclingOrder.Asset.Base
{
    public abstract class RecyclingOrderAssetDataSourceBuilderBase<TRequestModel, TDataSource> : DataSourceBuilderBase<TRequestModel, TDataSource>
        where TRequestModel : DocumentRequestBase
        where TDataSource : RecyclingOrderAssetReportDataSourceBase, new()
    {
        public override TDataSource Build(TRequestModel rq) =>        
            base.Build(rq);
        
        protected RecyclingOrderAssetDataSourceBuilderBase(ILogoHelper logoHelper, 
            IUserDataService userDataService, 
            IContextVariablesProvider context, 
            ICustomerDataService customerDataService, 
            ICompanyDataService companyDataService, 
            IAddressMapper addressMapper, 
            ICrmAccountReportService crmAccountReportService, 
            IConfigDbContextDataService dsConfigDb) 
            : base(logoHelper, userDataService, context, customerDataService, companyDataService, addressMapper, crmAccountReportService, dsConfigDb)
        {
        }
    }
}
