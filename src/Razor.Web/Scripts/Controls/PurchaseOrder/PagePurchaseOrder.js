function PagePurchaseOrder() {
    this.onCreate();
};

PagePurchaseOrder.prototype = {
    isPoMode: true,

    _urls: {
        editCustomer:           Common.baseUrl + '/Admin/CRM/Accounts.aspx?customerId={0}&editActiveTab=0',
        receiveOrder:           Common.baseUrl + '/Admin/PurchaseOrderReceive.aspx?orderId=',
        createOrder:            Common.baseUrl + '/Admin/PurchaseOrder.aspx',
        orderList:              Common.baseUrl + '/Admin/PurchaseOrders.aspx',
        editOrder:              Common.baseUrl + '/Admin/PurchaseOrder.aspx?orderId=',
        refInboundOrder:        Common.baseUrl + '/Admin/RecyclingOrderSettle.aspx?orderId=',
        refOutboundOrder:       Common.baseUrl + '/Admin/OutboundOrderSettle.aspx?orderId=',
        listPoTerms:            Common.baseUrl + '/Services/SalesOrderService.asmx/ListTermsByType',
        listCustomers:          Common.baseUrl + '/Services/CustomerService.asmx/ListAllCustomers',
        listUsers:              Common.baseUrl + '/Services/UserService.asmx/ListActiveRepUsers',
        load:                   Common.baseUrl + '/Services/PurchaseOrdersService.asmx/LoadPurchaseOrder',
        save:                   Common.baseUrl + '/Services/PurchaseOrdersService.asmx/SavePurchaseOrder',
        loadDynamicInfo:        Common.baseUrl + '/Services/PurchaseOrdersService.asmx/GetPurchaseOrderDynamicInfo',
        setOrderStatus:         Common.baseUrl + '/Services/PurchaseOrdersService.asmx/SetOrderStatus',
        deleteOrder:            Common.baseUrl + '/Services/PurchaseOrdersService.asmx/DeleteOrder',
        getDefaultTerm:         Common.baseUrl + '/Services/SalesOrderService.asmx/GetDefaultCustomerTransactionTerm',
        goToCreateCustomer:     Common.baseUrl + '/Admin/CRM/Accounts.aspx?customerName=',
        downloadOrderItemsPdf:  Common.baseUrl + '/Services/PurchaseOrdersService.asmx/DownloadPurchaseOrderItemsPdf?orderId={0}&isReceive={1}&isResale={2}&isViewAggregated={3}&checkboxes={4}&pageOrientation={5}',
        previewOrderItemsPdf:   Common.baseUrl + '/Services/PurchaseOrdersService.asmx/PreviewPurchaseOrderItemsPdf?orderId={0}&isReceive={1}&isResale={2}&isViewAggregated={3}&checkboxes={4}&pageOrientation={5}',
        salesOrderEdit: Common.baseUrl + '/Admin/SalesOrder.aspx?orderId={0}',
        downloadOrderPdf:       Common.baseUrl + '/Services/RecyclingService.asmx/DownloadInboundDraftAssetsPdf?recyclingOrderId={0}',
    },

    _sel: {
        acCustomer:         '#dd_PurchaseOrder_Vendor',
        laCustomer:         '.label-purchase-order-customer',
        aEditCustomer:      '#dd_PurchaseOrder_Vendor_Edit',
        acUser:             '#dd_PurchaseOrder_User',
        ddTerms:            '#dd_PurchaseOrder_Terms',
        tbDate:             '#tb_PurchaseOrder_Date',
        laOrderId:          '.label-puchase-order-id',
        lblWarehouse:       '#lbl_warehouse',
        aReferenceOrderId:   '#a_PurchaseOrder_InboundOrder_AutoName',
        laStatus:           '.label-puchase-order-status',
        laSubtotal:         '#lb_PurchaseOrder_Subtotal',
        spanTax:            '#span_Tax',
        spanAmount:         '#span_Amount',
        laIsResale:         '#lb_PurchaseOrder_IsResale',
        laPayments:         '.label-puchase-order-payments-applied',
        inReference:        '#in_PurchaseOrder_Reference',
        taComments:         '#ta_PurchaseOrder_Comments',
        taCommentsInternal: '#taCommentsInternal',
        taReceivingNotes:   '#taReceivingNotes',
        liSalesOrder:       '#li_SalesOrder',
        aSalesOrder: '#a_SalesOrder',
        laSubtotalCurrency: '#lb_PurchaseOrder_SubtotalCurrency',
        laPaymentsCurrency: '#lb_PurchaseOrder_PaymentsCurrency'
    },

    _data: {
        PurchaseOrderId: null,
        CustomerId: null,
        Invoice: null,
        SalesOrder: null,
        RecyclingOrderId: null
    },

    _controls: {
        acCustomer:     null,
        acUser:         null,
        menu:           null,
        grid:           null,
        printDialog: null,
        templatePrintDialog: null,
        orderDate: null
    },

    _appliedTax: null,
    _costs: null,

    Events: {
        StatusChanged: [],
        CustomerChanged: [],
        BulkReceive:[]
    },

    onCreate: function () {
        var it = this;
        // customer autocomplete
        it._controls.acCustomer = new UnifiedAutocomplete(it._sel.acCustomer, it._urls.listCustomers, {
            minLength: 0,
            staticLoad: false,
            discardUnknown: true,
            restoreIfEmpty: true
        },
        {
            afterSelect: function(text, item) {
                it.onCustomerSelected(item, true);
                if (item) {
                    if (it._data.PurchaseOrderId) {
                        // open address tab
                        invokeEvent(it.Events.CustomerChanged);
                        NoticeAlert.showLightInfo('Information', "The customer was changed. Please, correct the Vendor address.");
                    }
                    $(it._sel.laCustomer).text(item.label);
                    it.save();
                }
                else
                {
                    if (text.length > 0) {
                        Url.openNewTab(it._urls.goToCreateCustomer + text);
                    }
                }
            }
        });

        // user autocomplete
        it._controls.acUser = new UnifiedAutocomplete(it._sel.acUser, it._urls.listUsers, {
            minLength: 0,
            staticLoad: false,
            discardUnknown: true,
            restoreIfEmpty: true
        },
        {
            beforeSearch: function (params) { params.viewType = NameOutputTypes.FullName; },
            afterSelect: function(text, item) {
                if (item) {
                    it.save();
                }                
            }
        });

        $(it._sel.acUser).loadCurrentUserToAutoCompleteSync();

        // terms
        var enabled = $(it._sel.ddTerms).rebuildSelectOptionsSync(it._urls.listPoTerms, { isPoTerms: true }).isActionAllowed();
        $(it._sel.ddTerms).toggleEnabled(enabled);
        if (enabled) {
            $(it._sel.ddTerms).change($.proxy(it.save, it));
        }

        // date
        it._controls.orderDate = $(it._sel.tbDate).makeDatePicker(null, null, () => it.save());

        // Events
        $(it._sel.taComments).add(it._sel.taCommentsInternal).add(it._sel.taReceivingNotes).add(it._sel.inReference).change($.proxy(it.save, it));
        
        // menu
        it._controls.menu = new ActionButtons({
            onNew: function (event) {
                event.preventDefault();
                ShowConfirm('A blank order will be created.<br/>Continue?', null, function () {
                    Url.redirect(it._urls.createOrder);
                });
            },

            onApprove: function (event) {
                event.preventDefault();
                it.SetOrderStatus(PurchaseOrderStatuses.AwaitingConfirm);
            },

            onReceive: function(event) {
                event.preventDefault();
                if (it._data.PurchaseOrderId) {
                    Url.redirect(it._urls.receiveOrder + it._data.PurchaseOrderId);
                }
            },

            onChange: function (event) {
                event.preventDefault();
                it.SetOrderStatus(PurchaseOrderStatuses.Changed, function() {
                    var windowUrl = Url.getParameterlessUrl() + '?orderId=';
                    if (windowUrl != it._urls.editOrder) {
                        Url.redirect(it._urls.editOrder + it._data.PurchaseOrderId);
                    }
                }, true);
            },

            onReceiveComplete: function(event) {
                event.preventDefault();
                it.SetOrderStatus(PurchaseOrderStatuses.Received, true);
            },

            onCancel: function (event) {
                event.preventDefault();
                it.SetOrderStatus(PurchaseOrderStatuses.Canceled, true);
            },

            onDelete: function (event) {
                event.preventDefault();
                it.DeleteOrder();
            },

            onPrint: function (event) {
                event.preventDefault();
                it.OpenPrintDialog();
            },

            onPrintNew: event => {
                event.preventDefault();
                it.OpenReportPrintDialog();
            },

            onUndoReceived: function (event) {
                event.preventDefault();
                it.SetOrderStatus(PurchaseOrderStatuses.AwaitingConfirm);
            },
            onBulkReceive: function(event) {
                event.preventDefault();
                var customer = { value: it._data.CustomerId, label: $(it._sel.laCustomer).first().text() }
                invokeEvent(it.Events.BulkReceive, it._data.PurchaseOrderId, customer, it._data.SalesOrder);
            }
        });
    }, 

    toggleButtonsVisibility: function(doShow) {
        var it = this;
        if (typeof doShow === "undefined") {
            doShow = it._data.IsResale;
        }
        it._controls.menu.ToggleVisibility({
            New:             true,
            Approve:         doShow,
            Receive:         doShow,
            Change:          doShow,
            ReceiveComplete: doShow,
            Cancel:          doShow,
            Delete:          doShow && !it.HasOrderRefId(),
            Payment:         true,
            //Print:           doShow,
            UndoReceived:    doShow && it._data.StatusId == PurchaseOrderStatuses.Received,
            BulkReceive:     true
        });
    },

    toggleButtons: function () {
        var it = this;
        var anyItems = it._data.ItemCount > 0;
        it._controls.menu.ToggleEnabled({
            New: true,
            Approve:
                anyItems && (it._data.StatusId == PurchaseOrderStatuses.AwaitingApproval ||
                it._data.StatusId == PurchaseOrderStatuses.Changed),
            Receive:
                anyItems && (it._data.StatusId == PurchaseOrderStatuses.AwaitingConfirm ||
                it._data.StatusId == PurchaseOrderStatuses.PartiallyReceived ||
                it._data.StatusId == PurchaseOrderStatuses.Received),
            Change: it._data.StatusId == PurchaseOrderStatuses.PartiallyReceived ||
                it._data.StatusId == PurchaseOrderStatuses.Received ||
                it._data.StatusId == PurchaseOrderStatuses.Canceled,
            ReceiveComplete:
                /*it._data.InTransitCount > 0 && */(
                it._data.StatusId == PurchaseOrderStatuses.AwaitingConfirm ||
                it._data.StatusId == PurchaseOrderStatuses.PartiallyReceived),
            Cancel: it._data.StatusId != PurchaseOrderStatuses.Canceled,
            Delete: it._data.IsResale && !it.HasOrderRefId(),
            Print: anyItems,
            PrintNew: anyItems,
            UndoReceived: it._data.StatusId == PurchaseOrderStatuses.Received,
            BulkReceive:  false
        });
    },

    ToggleBulkReceive: function(value) {
        var it = this;
        it._controls.menu.ToggleEnabled({ BulkReceive: value });
    },

    InvoiceSelected: function(rows) {
        var it = this;
        if (rows.length >= 1) {
            it._data.Invoice = rows[0];
        } else {
            it._data.Invoice = null;
        }
        it.toggleButtons();
    },

    IsResale: function() {
        var it = this;
        return it._data.IsResale;
    },

    IsEditable: function() {
        var it = this;
        return it._data.StatusId == PurchaseOrderStatuses.AwaitingConfirm ||
                it._data.StatusId == PurchaseOrderStatuses.PartiallyReceived;
    },

    HasOrderRefId: function () {
        var it = this;
        return Number.isNumber(it._data.OrderRefId);
    },

    SetOrderStatus: function(statusId, onChanged) {
        var it = this;
        ShowConfirm('The status of the order will be changed to <b>' + PurchaseOrderStatuses.getTextName(statusId) + '</b>.<br/>Continue?', null,
            function () {
                var response = Url.PostSync(it._urls.setOrderStatus, { orderId: it._data.PurchaseOrderId, statusId: statusId });
                ProcessAjaxItemResponse(response, function (poItems) {
                    if (poItems && poItems.length > 0 && statusId == PurchaseOrderStatuses.Received && it._data.SalesOrder) {
                        _.each(poItems, function(poItem) {
                            poItem.InventoryItemId = poItem.ItemInventoryId;//misspelled
                        });
                        PoCommon.AllocateSrcSoItemsIfNeeded(poItems, it._data.SalesOrder.value, it._data.SalesOrder.label, ready);
                    } else {
                        ready();
                    }
                });
            });

        function ready() {
            if (!$.isFunction(onChanged)) {
                it.Recalculate();
                return;
            }

            onChanged(statusId);
            it.Recalculate(true);
        }
    },

    DeleteOrder: function() {
        var it = this;
        ShowConfirm('The order will be <b>deleted</b>.<br/>Continue?', null, function () {
            var response = Url.PostSync(it._urls.deleteOrder, { orderId: it._data.PurchaseOrderId });
            ProcessAjaxItemResponse(response, function() {
                Url.redirect(it._urls.orderList);
            });            
        });
    },

    Load: function(poId) {
        const it = this;
       
        poId = Number.parse(poId, null);
        if (poId) {
            var response = Url.PostSync(it._urls.load, { purchaseOrderId: poId });
            ProcessAjaxItemResponse(response, function (order) {
                if (!order) {
                    NoticeAlert.showError('Not found', 'The purchase order is not found. Navigating you to the list...');
                    _.delay(() => {
                        Url.redirect(it._urls.orderList);
                    }, 2500);
                    return;
                }

                if (it.isPoMode && order.IsResale) {
                    Url.redirect(Common.baseUrl + '/Admin/PurchaseOrder.aspx?orderId=' + poId); // a list with common routes would be much better
                    return;
                }

                it.LoadModel(order);
            });
        } else {
            it.toggleButtonsVisibility(true);
        }
    },

    LoadModel: function(model) {
        var it = this;

        $.extend(it._data, model);

        const currency = PurchaseOrderContext.Load(it._data.PurchaseOrderId);
        $(it._sel.laSubtotalCurrency).text(currency);
        $(it._sel.laPaymentsCurrency).text(currency);

       
        $(it._sel.laOrderId).text(model.AutoName);
        $(it._sel.lblWarehouse).text(model.Warehouse);
        $(it._sel.laStatus).text(model.StatusCd);
        $(it._sel.laIsResale).text(model.IsResale ? 'Yes' : 'No');
        $(it._sel.aReferenceOrderId).text(String.nullTo(model.OrderAutoName, 'None'));
        $(it._sel.inReference).val(model.Reference);
        $(it._sel.taComments).val(model.Comments);
        $(it._sel.taCommentsInternal).val(model.InternalComments);
        $(it._sel.taReceivingNotes).val(model.ReceivingNotes);
        if (Number.isNumber(model.OrderRefId)) {
            $(it._sel.aReferenceOrderId)
                .toggleClass('void-link', false)
                .prop('href',
                    model.RefOrderIsInbound
                        ? it._urls.refInboundOrder + model.OrderRefId
                        : it._urls.refOutboundOrder + model.OrderRefId)
                .click(function () { return true; });
        } else {
            $(it._sel.aReferenceOrderId)
                .toggleClass('void-link', true)
                .prop('href', '#')
                .click(function (event) { event.preventDefault(); });
        }

        if (Number.isNumber(model.CustomerId)) {
            it._data.CustomerId = model.CustomerId;
            it._controls.acCustomer.SetData({
                label: model.CustomerCd,
                value: model.CustomerId
            });
            if (it._data.StatusId > PurchaseOrderStatuses.AwaitingConfirm) {
                $(it._sel.acCustomer).prop('disabled', true);
            }
            $(it._sel.laCustomer).text(model.CustomerCd);
            $(it._sel.aEditCustomer).prop('href', String.format(it._urls.editCustomer, model.CustomerId)).show();
        } else {
            $(it._sel.aEditCustomer).prop('href', '#').hide();
        }
        if (Number.isNumber(model.UserId)) {
            it._controls.acUser.SetData({
                label: model.UserName,
                value: model.UserId
            });
        }
        $(it._sel.ddTerms).val(model.PoTermsId);
        it._controls.orderDate.val(DateTimeFormat.toString(model.DateTime, false, false, true));

        if (Number.isNumber(model.SalesOrderId)) {
            $(it._sel.liSalesOrder).show();
            $(it._sel.aSalesOrder).text(model.SalesOrderName);
            $(it._sel.aSalesOrder).attr("href", String.format(it._urls.salesOrderEdit, model.SalesOrderId));
            it._data.SalesOrder = { value: model.SalesOrderId, label: model.SalesOrderName };
        } else {
            $(it._sel.liSalesOrder).hide();
            it._data.SalesOrder = null;
        }
        it.Recalculate(true);
        it.toggleButtonsVisibility();
    },

    onCustomerSelected: function(item, updateTerms) {
        var it = this;
        if (item) {
            $(it._sel.aEditCustomer).prop('href', String.format(it._urls.editCustomer, item.value)).show();
            // update terms
            if (updateTerms) {
                var response = Url.PostSync(it._urls.getDefaultTerm, { customerId: item.value, isSt: false });
                ProcessAjaxItemResponse(response, function(termItem) {
                    if (termItem) {
                        $(it._sel.ddTerms).val(termItem.value);
                    }
                });
            }
        } else {
            $(it._sel.aEditCustomer).prop('href', '#').hide();
        }
    },

    save: function() {
        var it = this;
        // cannot create orders in non-creation mode
        if (!Number.isNumber(it._data.PurchaseOrderId)) {
            function openNew() {
                Url.redirect(it._urls.createOrder);
            }
            ShowAlert('You cannot create a purchase order in this mode.', null, openNew, openNew);
            return;
        }

        var customerId = it._controls.acCustomer.GetDataId();
        if (Number.isNumber(it._data.PurchaseOrderId) || customerId != null) {
            var params = {
                PurchaseOrderId: it._data.PurchaseOrderId,
                CustomerId: customerId,
                PoTermsId: $(it._sel.ddTerms).val(),
                UserId: it._controls.acUser.GetDataId(),
                DateTime: DateTimeFormat.toISOString(it._controls.orderDate.val(), false, false, true),
                Reference: $(it._sel.inReference).val(),
                Comments: $(it._sel.taComments).val(),
                InternalComments: $(it._sel.taCommentsInternal).val(),
                ReceivingNotes: $(it._sel.taReceivingNotes).val()
            };
            var response = Url.PostSync(it._urls.save, { purchaseOrder: params });
            ProcessAjaxItemResponse(response, function (purchaseOrderId) {
                if (!Number.isNumber(it._data.PurchaseOrderId) && Number.isNumber(purchaseOrderId)) {
                    Url.redirect(it._urls.editOrder + purchaseOrderId);
                    //it.LoadModel(model);
                } else {
                    it.Recalculate();
                }
            });
        }
    },

    Recalculate: function(castEvent) {
        var it = this;
        if (!Number.isNumber(it._data.PurchaseOrderId)) {
            return;
        }
        Url.PostAsync(it._urls.loadDynamicInfo, {purchaseOrderId: it._data.PurchaseOrderId, isReceive: true}, function(response) {
            ProcessAjaxItemResponse(response, function (model) {
                it._costs = model;
                it.RecalculateHtml(castEvent);
            });
        });
    },

    RecalculateHtml: function (castEvent) {
        var it = this;

        var isTaxNotSaved = Number.isNumber(it._appliedTax);
        var model = it._costs;
        
        const canUseInvoicedAmount = !it._data.IsResale && model.InvoicedAmount;
        $(it._sel.laSubtotal).text(canUseInvoicedAmount ? model.InvoicedAmount.toFixed(2) : model.Subtotal.toFixed(2));
        $(it._sel.spanTax).text((isTaxNotSaved ? it._appliedTax : model.Tax).toFixed(2));
        $(it._sel.spanAmount).text(((isTaxNotSaved ? model.Subtotal + it._appliedTax : model.Subtotal + model.Tax) - model.PaidAmount).toFixed(2));
        $(it._sel.laPayments).text(model.PaidAmount);
        $(it._sel.laStatus).text(PurchaseOrderStatuses.getTextName(model.StatusId));
        var statusUpdated = castEvent === true || model.StatusId != it._data.StatusId;
        $.extend(it._data, model);
        it.toggleButtonsVisibility();
        it.toggleButtons();
        if (statusUpdated) {
            invokeEvent(it.Events.StatusChanged, it._data.PurchaseOrderId, it._data.StatusId);
        }
    },

    GetCustomerId: function () {
        var it = this;
        return it._data.CustomerId;
    },

    GetSalesOrderInfo: function () {
        var it = this;
        return {
            SalesOrderId: it._data.SalesOrderId,
            SalesOrderName: it._data.SalesOrderName
        }
    },

    OpenPrintDialog: function () {
        var it = this;
     
        if (!it._controls.printDialog) {
            it._controls.printDialog = new PrintDialog([
                {
                    Selectors: {
                        divDialog:          '#div_PrintDialog_OnPage',
                        divContainer:       '#div_ReportsContainer_OnPage',
                        ddPrinterName:      '#dd_ReportsPrinterName_OnPage',
                        spQty:              '#sp_ReportsPrintQty_OnPage',
                        ddPageOrientation:  '#dd_PageOrientation_OnPage'
                    },
                    SettingName: PrinterSettings.PurchaseOrderItems.SettingName,
                    Documents:
                    [
                        {
                            Title: 'Audit Report',
                            onPrint: function (id, printerName, qty, onReady) {
                                var templateIds = _.map($('input#dd_NewAuditReportTempltes').tokenInput('get'), function (item) {
                                    return item.value;
                                });

                                if (templateIds.length === 0) {
                                    NoticeAlert.showWarning('Attention', 'Please select at least one Template for the Audit Report');
                                    onReady(false);
                                    return;
                                }

                                Printing.PrintAuditReport(id, null, templateIds, printerName, qty, onReady);
                            },
                            onDownload: function (id, onReady) {
                                var templates = $('input#dd_NewAuditReportTempltes').tokenInput('get');
                                if (templates.length === 0) {
                                    NoticeAlert.showWarning('Attention', 'Please select at least one Template for the Audit Report');
                                    onReady(false);
                                    return;
                                }

                                // for each template
                                var qty = templates.length;
                                var fnReady = function () {
                                    qty--;
                                    if (qty <= 0) {
                                        onReady(true);
                                    }
                                }

                                _.each(templates, function (item) {
                                    var model = encodeURIComponent(JSON.stringify({
                                        RecyclingOrderId: id,
                                        AuditId: null,
                                        TemplateIds: [item.value]
                                    }));
                                    Printing.downloadSimple(Common.baseUrl + '/Services/PrinterService.asmx/DownloadAuditReportNew?model={0}', model, fnReady);
                                });
                            },
                            onPreview: function (id, onReady) {
                                var templates = $('input#dd_NewAuditReportTempltes').tokenInput('get');
                                if (templates.length === 0) {
                                    NoticeAlert.showWarning('Attention', 'Please select at least one Template for the Audit Report');
                                    onReady(false);
                                    return;
                                }

                                // for each template
                                var qty = templates.length;
                                var fnReady = function () {
                                    qty--;
                                    if (qty <= 0) {
                                        onReady(true);
                                    }
                                }

                                _.each(templates, function (item) {
                                    var model = encodeURIComponent(JSON.stringify({
                                        RecyclingOrderId: id,
                                        AuditId: null,
                                        TemplateIds: [item.value]
                                    }));
                                    Printing.previewSimple(Common.baseUrl + '/Services/PrinterService.asmx/PreviewAuditReportNew?model={0}', model, fnReady);
                                });
                            },
                            individualOptions: {
                                getParams: function ($div) {
                                    debugger;
                                },
                                BeforeOpen: function ($div) {
                                    var docDef = this;

                                    // prepare the templates token input
                                    docDef._templates = [];
                                    var $tokenInput = $div.find('input#dd_NewAuditReportTempltes[token-input-applied]');
                                    if ($tokenInput.length === 0) {
                                        $tokenInput = $div.find('input#dd_NewAuditReportTempltes:not([token-input-applied])');
                                        $tokenInput.buildTokenInput(function () {
                                            return docDef._templates;
                                        });
                                        $tokenInput.attr('token-input-applied', true);
                                    } else {
                                        $tokenInput.tokenInput('clear');
                                    }


                                    // no data - disable all
                                    var disabled = !it.HasOrderRefId();
                                    var $checkbox = $div.find('input[type="checkbox"]');
                                    $checkbox.add($tokenInput).prop('disabled', disabled);
                                    if (disabled) {
                                        $checkbox.prop('checked', false);
                                        return;
                                    }

                                    // bind the actual templates
                                    var response = Url.GetSync(Common.baseUrl + '/Services/RecyclingService.asmx/ListRecyclingOrderInboundCertificates?recyclingOrderId=' + it._data.OrderRefId);
                                    Url.ProcessAjaxAnswer(response, function (templates) {
                                        docDef._templates = templates;

                                        // prefill with all available templates
                                        $tokenInput.tokenInput('add', docDef._templates);
                                    });
                                },
                                html: function () {
                                    return Url.GetTemplate(Common.baseUrl + '/Scripts/Controls/Admin/Recycling/NewAuditReportSection.html');
                                }
                            }
                            }
                    ],
                    PageOrientation:
                    [
                        {
                            Title: PageOrientation.getTextName(PageOrientation.Portrait),
                            Value: PageOrientation.Portrait,
                            Selected: true
                        },
                        {
                            Title: PageOrientation.getTextName(PageOrientation.Landscape),
                            Value: PageOrientation.Landscape,
                            Selected: false
                        }
                    ]
                }
            ], {
                divContainer:       '#div_PrintDialog_OnPage',
                divComplete:        '#div_CompletenessIndicator_OnPage',
                divOptions:         '#div_OptionsContainer_OnPage',
                divPageOrientation: '#div_PageOrientation_OnPage'
            });
        }

        it._controls.printDialog.Open(it._data.OrderRefId, null, 336, 530);
        
    },

    OpenReportPrintDialog: function () {
        const it = this;

        const data = it._data;

        if (!data) return;

        if (!it._controls.templatePrintDialog) {
            it._controls.templatePrintDialog = new Ractive.components.XtraTemplatePrintDialog({
                el: '#div_templatePrintDialog',
                TemplateTypes: [
                    {
                        Id: XtraTemplateTypes.Report,
                        TabName: 'Reports',
                        EntityTypes: [XtraDocumentEntities.Reports.PurchaseOrderRecycling]
                    }],
                Suffix: 'PurchaseOrderRecycling'
            });
        }

        const mappedRow = [{
            EntityId: data.PurchaseOrderId,
            Name: data.AutoName,
            ActualEntityTypes:
            {
                [XtraTemplateTypes.Report]: data.PurchaseOrderId && !data.IsResale ?
                    [XtraDocumentEntities.Reports.PurchaseOrderRecycling]
                    : []
            }
        }];

        it._controls.templatePrintDialog.showDialog(mappedRow);
    }
};