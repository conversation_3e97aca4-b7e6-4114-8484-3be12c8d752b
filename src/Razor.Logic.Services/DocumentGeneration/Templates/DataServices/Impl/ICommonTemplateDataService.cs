using System.Collections.Generic;
using Razor.Logic.Services.DocumentGeneration.Templates.DataServices.Model;
using Razor.Tools.DataAccess.DataServiceBase;

namespace Razor.Logic.Services.DocumentGeneration.Templates.DataServices.Impl
{
    public interface ICommonTemplateDataService : IBaseRepository
    {
        IEnumerable<DocumentTemplateInfo> ListByEntityTypes(IEnumerable<int> entityTypes);
        DocumentTemplate GetDocumentTemplate(long documentId);
        DocumentTemplateInfo GetByTypeAndName(int entityTypeId, string name);
    }
}