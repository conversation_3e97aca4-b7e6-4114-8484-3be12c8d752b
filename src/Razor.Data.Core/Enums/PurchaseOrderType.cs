using Razor.Common.Base.Attributes.ForEnums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Razor.Data.Core.Enums
{
    /*[D_ENTITY_SUBJECT_TYPE] where ENTITY_ID = 3*/
    public enum PurchaseOrderType
    {
        /// <summary>
        /// Equipment Purchase
        /// </summary>
        [EnumName(TextName = "Equipment Purchase")]
        EquipmentPurchase = 6,
        /// <summary>
        /// Consignment
        /// </summary>
        [EnumName(TextName = "Consignment")]
        Consignment = 7,
        /// <summary>
        /// Drop Ship
        /// </summary>
        [EnumName(TextName = "DropShip")]
        DropShip = 8,
        /// <summary>
        /// Recycling Consignment
        /// </summary>
        [EnumName(TextName = "Recycling Consignmen")]
        RecyclingConsignment = 10,
        /// <summary>
        /// Inbound Repair
        /// </summary>
        [EnumName(TextName = "Inbound Repair")]
        InboundRepair = 11,
        /// <summary>
        /// Linked Purchase Order
        /// </summary>
        [EnumName(TextName = "Linked Purchase Order")]
        LinkedPurchaseOrder = 25
    }
}
