using System;
using Razor.Cache.EventBuffer.Base;
using Redis.OM.Modeling;

namespace Razor.Cache.EventBuffer.SkuRebuild
{
    [Document (IndexName = "sku-rebuilds")]
    public class SkuRebuildEvent: IEquatable<SkuRebuildEvent>, IEvent
    {
        [RedisIdField]
        public string Id => $"{CompanyId}:{EventType}:{SkuId}";

        [Indexed(Sortable = true, Aggregatable = true)]
        public long CompanyId { set; get; }

        [Indexed(Sortable = true, Aggregatable = true)]
        public int EventType { set; get; }

        [Indexed(Sortable = true, Aggregatable = true)]
        public long SkuId { set; get; }

        public bool Equals(SkuRebuildEvent x, SkuRebuildEvent y)
        {
            return x.Id.Equals(y.Id);
        }

        public bool Equals(SkuRebuildEvent other)
        {
            return Equals(this, other);
        }
    }
}
