using System;
using System.Collections.Generic;


namespace Razor.Logic.Services.LogicServices.Interfaces.Shipping.Models
{
    public class ShippingPrintResult
    {
        public Int64 SalesOrderId { get; set; }
        public String SalesOrderNumber { get; set; }
        public Boolean IsShipped { get; set; }
        public Boolean WasShipped { get; set; }
        public String AdditionalMessage { get; set; }
        public Int64 ShippingId { get; set; }
        public List<SalesOrderPackagePrintResult> Packages { get; set; }
    }
}
