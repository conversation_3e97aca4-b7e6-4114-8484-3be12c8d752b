using System;
using System.Collections.Generic;

using Razor.Data.Core.DataServices.Report.Models.ReportLogs;

namespace Razor.Data.Core.DataServices.Report
{
    public interface IReportLogDataService
    {
        Int64 StartReport(ReportLogModel reportLog);
        void EndReport(Int64 reportLogId);

        IEnumerable<ReportLogModel> GetPage(ReportLogFilter pageRequest, out Int64 totalRecords);
    }
}
