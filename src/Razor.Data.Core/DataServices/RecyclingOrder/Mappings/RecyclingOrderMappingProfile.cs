using AutoMapper;
using Razor.Data.Core.DataServices.RecyclingOrder.Models;

namespace Razor.Data.Core.DataServices.RecyclingOrder.Mappings
{
    public class RecyclingOrderMappingProfile : Profile
    {
        public RecyclingOrderMappingProfile()
        {
            CreateMap<RecyclingOrderGridItem, RecyclingOrderGridItem>()
                .ForMember(dest => dest.LogisticTypeCd,
                opt => opt.MapFrom(src =>
                    src.IsTransfer ? src.LogisticTypeCd + "/Internal Transfer"
                    : src.IsRemoteReturn ? src.LogisticTypeCd + "/Remote Return"
                    : src.LogisticTypeCd));
        }
    }
}
