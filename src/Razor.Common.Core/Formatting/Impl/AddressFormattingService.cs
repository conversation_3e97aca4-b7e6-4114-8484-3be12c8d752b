using System;
using System.Collections.Generic;
using System.Data;
using Razor.Common.Base.Helpers.Countries;
using Razor.Common.Core.Addresses;
using Razor.Common.Core.UserAccess.Data;
using Razor.Tools.DataAccess.Helpers;

namespace Razor.Common.Core.Formatting.Impl
{
    public class AddressFormattingService : IAddressFormattingService
    {
        private readonly IUserAccessDataService _userDataService;

        public AddressFormattingService(IUserAccessDataService userDataService)
        {
            _userDataService = userDataService;
        }  
        

        public string[] FormatPostalAddress(AddressModel address, bool includePhone = true, bool includeFax = false, bool isCountryInLineAfterZip = false)
        {                       
            var result = new List<String>();                      

            if (!String.IsNullOrWhiteSpace(address.CompanyName))
            {
                result.Add(address.CompanyName);
            }

            if (!String.IsNullOrWhiteSpace(address.ContactName) && address.CompanyName != address.ContactName)
            {
                result.Add(address.ContactName);
            }

            if (!String.IsNullOrWhiteSpace(address.Street1))
            {
                result.Add(address.Street1);
            }

            if (!String.IsNullOrWhiteSpace(address.Street2))
            {
                result.Add(address.Street2);
            }

            if (!String.IsNullOrWhiteSpace(address.Street3))
            {
                result.Add(address.Street3);
            }

            if (!(String.IsNullOrWhiteSpace(address.City) && String.IsNullOrWhiteSpace(address.State) && String.IsNullOrWhiteSpace(address.PostalCode)))
            {
                result.Add(String.Format("{0}, {1} {2}", address.City, address.State, address.PostalCode));
            }
            if (!String.IsNullOrWhiteSpace(address.Country))
            {
                if (isCountryInLineAfterZip && result.Count > 0)
                {
                    result[result.Count - 1] += String.Format(" {0}", GetCountryDisplayName(address.Country));
                }
                else
                {
                    result.Add(GetCountryDisplayName(address.Country));
                }
            }
            if (includePhone && !String.IsNullOrWhiteSpace(address.Phone))
            {
                String fax = String.Empty;
                if (includeFax && !String.IsNullOrEmpty(address.Fax))
                {
                    fax = String.Format(" F: {0}", address.Fax);
                }
                result.Add(String.Format("P: {0}{1}", address.Phone, fax));
            }
            return result.ToArray();
        }

        public string[] FormatCompanyAddress(AddressModel address, bool fullFormat, bool extraBreaks = false, bool exludeCountry = false)
        {            
            var lines = new List<String>();
            if (!String.IsNullOrWhiteSpace(address.CompanyName))
            {
                lines.Add(address.CompanyName);
            }

            if (!String.IsNullOrWhiteSpace(address.Street1))
            {
                lines.Add(address.Street1);
            }

            if (!String.IsNullOrWhiteSpace(address.Street2))
            {
                lines.Add(address.Street2);
            }

            if (!String.IsNullOrWhiteSpace(address.Street3))
            {
                lines.Add(address.Street3);
            }

            if (!(String.IsNullOrWhiteSpace(address.City) &&
                  String.IsNullOrWhiteSpace(address.State) &&
                  String.IsNullOrWhiteSpace(address.PostalCode) &&
                  String.IsNullOrWhiteSpace(address.Country)))
            {
                var line = String.Format("{0}, {1} {2}", address.City, address.State, address.PostalCode);
                if (!fullFormat && !exludeCountry)
                {
                    line = String.Format("{0}, {1}", line, GetCountryDisplayName(address.Country));
                }
                lines.Add(line);
            }

            if (fullFormat)
            {
                lines.Add(GetCountryDisplayName(address.Country));
                if (!String.IsNullOrWhiteSpace(address.Phone))
                {
                    if (!extraBreaks)
                    {
                        lines.Add("P: " + address.Phone + (String.IsNullOrWhiteSpace(address.Fax)
                            ? ""
                            : ("  F: " + address.Fax)));
                    }
                    else
                    {
                        lines.Add("P: " + address.Phone);
                        if (!String.IsNullOrWhiteSpace(address.Fax))
                        {
                            lines.Add("F: " + address.Fax);
                        }
                    }
                }
            }
            return lines.ToArray();
        }

        public string[] FormatCustomerAddress(AddressModel address, string customerName, string contactName, bool includePhone = true, bool includeFax = false, bool isCompanyUse = true, bool includeVATandCoC = false)
        {            
            if (address == null)
            {
                return new String[0];
            }

            var result = new List<String>();
            if (isCompanyUse && !String.IsNullOrWhiteSpace(address.CompanyName))
            {
                customerName = address.CompanyName;
            }
            if (!String.IsNullOrWhiteSpace(customerName))
            {
                result.Add(customerName);
            }

            if (!String.IsNullOrWhiteSpace(address.ContactName))
            {
                contactName = address.ContactName;
            }

            if (!string.IsNullOrWhiteSpace(contactName) && contactName != customerName)
            {
                result.Add(contactName);
            }

            if (!String.IsNullOrWhiteSpace(address.Street1))
            {
                result.Add(address.Street1);
            }

            if (!String.IsNullOrWhiteSpace(address.Street2))
            {
                result.Add(address.Street2);
            }

            if (!String.IsNullOrWhiteSpace(address.Street3))
            {
                result.Add(address.Street3);
            }

            if (!(String.IsNullOrWhiteSpace(address.City) && String.IsNullOrWhiteSpace(address.State) && String.IsNullOrWhiteSpace(address.PostalCode)))
            {
                result.Add(String.Format("{0}, {1} {2}", address.City, address.State, address.PostalCode));
            }
            if (!String.IsNullOrWhiteSpace(address.Country))
            {
                result.Add(GetCountryDisplayName(address.Country));
            }
            if (includePhone && !String.IsNullOrWhiteSpace(address.Phone))
            {
                String fax = String.Empty;
                if (includeFax && !String.IsNullOrEmpty(address.Fax))
                {
                    fax = String.Format(" F: {0}", address.Fax);
                }
                result.Add(String.Format("P: {0}{1}", address.Phone, fax));
            }
            if (includeVATandCoC && !string.IsNullOrEmpty(address.VAT))
            {
                result.Add($"VAT: {address.VAT}");
            }
            if (includeVATandCoC && !string.IsNullOrEmpty(address.CommerceChamber))
            {
                result.Add($"CoC: {address.CommerceChamber}");
            }

            return result.ToArray();
        }

        public string[] FormatShortCustomerAddress(AddressModel address, string customerName, bool isJoinStreets = false, bool isCompanyUse = true)
        {            
            if (address == null)
            {
                return new string[0];
            }

            var result = new List<string>();
            
            if (isCompanyUse && !string.IsNullOrWhiteSpace(address.CompanyName))
            {
                customerName = address.CompanyName;
            }
            if (!string.IsNullOrWhiteSpace(customerName))
            {
                result.Add(customerName);
            }

            if (isJoinStreets)
            {
                var streets = new List<string>();
                if (!string.IsNullOrWhiteSpace(address.Street1))
                {
                    streets.Add(address.Street1);
                }

                if (!string.IsNullOrWhiteSpace(address.Street2))
                {
                    streets.Add(address.Street2);
                }

                if (!string.IsNullOrWhiteSpace(address.Street3))
                {
                    streets.Add(address.Street3);
                }
                result.Add(string.Join(", ", streets));
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(address.Street1))
                {
                    result.Add(address.Street1);
                }

                if (!string.IsNullOrWhiteSpace(address.Street2))
                {
                    result.Add(address.Street2);
                }

                if (!string.IsNullOrWhiteSpace(address.Street3))
                {
                    result.Add(address.Street3);
                }
            }

            if (!string.IsNullOrWhiteSpace(address.City))
            {
                result.Add(string.Format("{0}, {1} {2}", address.City, address.State, address.PostalCode));
            }

            return result.ToArray();
        }

        public string[] FormatCustomerAddressForPO(AddressModel address, string customerName, string contactName)
        {            
            if (address == null)
            {
                return new String[0];
            }

            var result = new List<String>();
            if (!String.IsNullOrWhiteSpace(address.CompanyName))
            {
                customerName = address.CompanyName;
            }
            if (!String.IsNullOrWhiteSpace(customerName))
            {
                result.Add(customerName);
            }

            if (!String.IsNullOrWhiteSpace(contactName) && customerName != contactName)
            {
                result.Add(String.Format("Attn: {0}", contactName));
            }

            if (!String.IsNullOrWhiteSpace(address.Street1))
            {
                result.Add(address.Street1);
            }

            if (!String.IsNullOrWhiteSpace(address.Street2))
            {
                result.Add(address.Street2);
            }

            if (!String.IsNullOrWhiteSpace(address.Street3))
            {
                result.Add(address.Street3);
            }

            if (!(String.IsNullOrWhiteSpace(address.City) && String.IsNullOrWhiteSpace(address.State) && String.IsNullOrWhiteSpace(address.PostalCode)))
            {
                result.Add(String.Format("{0}, {1} {2}", address.City, address.State, address.PostalCode));
            }
            if (!String.IsNullOrWhiteSpace(address.Country))
            {
                result.Add(GetCountryDisplayName(address.Country));
            }
            if (!String.IsNullOrWhiteSpace(address.Phone))
            {
                result.Add(String.Format("Phone: {0}", address.Phone));
            }

            if (!String.IsNullOrEmpty(address.Fax))
            {
                result.Add(String.Format(" Fax: {0}", address.Fax));
            }
            return result.ToArray();
        }

        public string[] FormatCustomerAddressForPO(DataRow row, String prefix, String customerName, String contactName)
        {
            var address = row.MapRow<AddressModel>(prefix);

            return FormatCustomerAddressForPO(address, customerName, contactName);
        }

        public string ToString(AddressModel address, string separator, Boolean fullFormat)
        {
            var lines = FormatCompanyAddress(address, fullFormat);
            return string.Join(separator, lines);
        }

        public string ToString(AddressModel address)
        {
            return ToString(address, Environment.NewLine, true);
        }        

        public string GetCountryDisplayName(string country)
        {
            var systemSettings = _userDataService.GetSystemSettings();
            bool isShowFullCountryName = systemSettings.IsFullCountryName;

            return isShowFullCountryName ? ShippingCountries.GetNameBy3LetterCode(country) : ShippingCountries.Get3LetterCodeByFullCountryName(country);
        }                      
    }
}
