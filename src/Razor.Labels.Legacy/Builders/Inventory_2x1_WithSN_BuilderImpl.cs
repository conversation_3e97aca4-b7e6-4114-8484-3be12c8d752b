using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using Razor.Common.Core.DateTimeFormat;
using Razor.Labels.Builders.Image;
using Razor.Labels.Enums;
using Razor.Labels.Models;
using Razor.Labels.Models.Inventory;
using Razor.Tools.Barcodes.Encoder;

namespace Razor.Labels.Legacy.Builders
{
    public class Inventory_2x1_WithSN_BuilderImpl : LabelBuilderBase<InventoryLabelModel>, IInventory_2x1_WithSN_Builder<LegacyLabel>
    {


        protected override IEnumerable<Bitmap> BuildBitmaps(InventoryLabelModel model, DrawingSettings drawingSettings)
        {
            var outputBitMap = new Bitmap(drawingSettings.PxDimensions.X, drawingSettings.PxDimensions.Y);
            outputBitMap.SetResolution(PrinterResolutionX, PrinterResolutionY);

            using (Graphics gr = Graphics.FromImage(outputBitMap))
            {
                gr.Clear(drawingSettings.BackColor);
                gr.SmoothingMode = SmoothingMode.HighQuality;
                gr.InterpolationMode = InterpolationMode.HighQualityBicubic;
                gr.PixelOffsetMode = PixelOffsetMode.HighQuality;
                gr.CompositingQuality = CompositingQuality.HighQuality;

                Single fontSizeInInches = 0.18f;
                var fontSize = ConvertInchesToPixels(fontSizeInInches, _printerResolutionX);
                using (var fontNormal = new Font("Arial", fontSize / 1.75f, FontStyle.Regular, GraphicsUnit.Pixel))
                using (var fontBold = new Font("Arial", fontSize / 1.75f, FontStyle.Bold, GraphicsUnit.Pixel))
                {
                    const Int32 heightStep = 500;
                    const Int32 marginConstX = 2;
                    const Int32 marginConstY = 2;

                    var caret = new CanvasCaret
                    {
                        Caret = new PointF(0, 0),
                        Brush = drawingSettings.TextBrush,
                        Graphics = gr,
                        PxDimensions = drawingSettings.PxDimensions
                    };

                    const Int32 paddingConstX = 50;
                    const Int32 paddingConstY = 15;

                    // Manufacturer

                    Int32 X = marginConstX + paddingConstX;
                    Int32 Y = marginConstY + paddingConstY;

                    caret.Caret = new PointF(X, Y);

                    gr.DrawString(model.Manufacturer, fontBold, drawingSettings.TextBrush,
                        new RectangleF(X, Y, caret.PxDimensions.X / (float)2.8, fontBold.Height));

                    // Category

                    Int32 labelWidth = (Int32)gr.MeasureString(model.Category, fontBold).Width;
                    X = caret.PxDimensions.X - marginConstX - paddingConstX - labelWidth;
                    Y = marginConstY + paddingConstY;

                    caret.Caret = new PointF(X, Y);

                    gr.DrawString(model.Category, fontBold, drawingSettings.TextBrush,
                        new RectangleF(X, Y, caret.PxDimensions.X * 2 / 3 - paddingConstX - marginConstX, fontBold.Height));

                    // Title

                    X = marginConstX + paddingConstX;
                    Y += fontBold.Height + 10;

                    caret.Caret = new PointF(X, Y);

                    gr.DrawString(model.ModelTitle, fontNormal, drawingSettings.TextBrush,
                        new RectangleF(X, Y, caret.PxDimensions.X - marginConstX - paddingConstX, fontNormal.Height));

                    // Part Number

                    Y += fontNormal.Height + 10;

                    Int32 barcodeWidth = caret.PxDimensions.X - marginConstX - paddingConstX;
                    Int32 barcodeHeight = 75;


                    //Y += barcodeHeight + 5;

                    caret.Caret = new PointF(X, Y);

                    AppendText(model.Model, fontBold, caret, heightStep, true);

                    // UID

                    Y += fontBold.Height + 10;

                    var b = new BarcodeEncoder(gr, new PointF())
                    {
                        Alignment = AlignmentPositions.LEFT,
                        Offset = new PointF(X, Y)
                    };
                    try
                    {
                        b.Encode(BarcodeFormats.CODE128, model.UniqueId, barcodeWidth, barcodeHeight);
                    }
                    catch (Exception)
                    {
                    }

                    Y += barcodeHeight + 5;

                    caret.Caret = new PointF(X, Y);

                    AppendText("UID: " + model.UniqueId, fontBold, caret, heightStep, true);


                    // Date

                    Y += fontBold.Height + 10;
                   
                    b = new BarcodeEncoder(gr, new PointF())
                    {
                        Alignment = AlignmentPositions.LEFT,
                        Offset = new PointF(X, Y)
                    };
                    try
                    {
                        b.Encode(BarcodeFormats.CODE128, model.Serial, barcodeWidth, barcodeHeight);
                    }
                    catch (Exception)
                    {
                    }
                    Y += barcodeHeight + 5;
                    caret.Caret = new PointF(X, Y);
                    AppendText("SN: " + model.Serial, fontBold, caret, heightStep, true);

                }


                gr.Flush();

                return new Bitmap[] { outputBitMap };
            }
        }

        protected override LabelTypes _labelType => LabelTypes.Size_2x1_Receive_InventoryWithSN;

        public Inventory_2x1_WithSN_BuilderImpl(IUserDateTimeFormatService userDateTimeFormatService) : base(userDateTimeFormatService)
        {
        }
    }
}
