using System;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
using System.Web.Security;
using CommonServiceLocator;
using Razor.Common.Base.Configuration;
using Razor.Common.Base.Context;
using Razor.Common.Core.DbLogging;
using Razor.Common.Core.DbLogging.Models;
using Razor.Common.ReportEngineAdapter.Configuration;
using Razor.Common.Web.UserAccess.Session;
using Razor.Data.Core;
using Razor.Logging.Connection;
using Razor.Logging.Connection.Configuration.Enums;
using Razor.Logic.Services.Extensions;
using Razor.Mq.MassTransitUnity;
using RazorWeb.Admin.Images;
using RazorWeb.RouteHandlers;
using Sentry.AspNet;

namespace RazorWeb
{
    public class Global : HttpApplication
    {
        private IDisposable _sentry;
        private const string AppName = "WebErp";

        void Application_Start(object sender, EventArgs e)
        {
            _sentry = Razor.Logging.Sentry.Startup.Init.InitializeSentryWithAspNet(AppName, "MassTransit:serilog:write-to");

            var vaultSecrets = Vault.FileProxy.Setup.ReadSecrets();
            RazorLogging.SetupSerilogLogger(
                AppName,
                () => vaultSecrets.Elastic.ConnectionString,
                LoggerOutputType.Console | LoggerOutputType.File
            );
            
            RegisterRoutes(RouteTable.Routes);
            ErpTypeHandlerRegistrations.RegisterTypeHandlers();
            BundleConfig.RegisterBundles(BundleTable.Bundles);
            BundleConfig.EnableOptimizationsByConfig();
            Bootstrapper.Init();
            EnsureConfiguration();
            Razor.Tools.DataAccess.Helpers.SqlOutput.IsWritingOutput =
#if DEBUG
            true;
#else
            true;
#endif

            MvcHandler.DisableMvcResponseHeader = true;

            
            MassTransitUnityBootstrap.StartBusThread();
        }

        void Application_End(object sender, EventArgs e)
        {
            MassTransitUnityBootstrap.StopBus();
            _sentry?.Dispose();
        }

        protected void Application_BeginRequest()
        {
            Context.StartSentryTransaction();
        }

        protected void Application_EndRequest()
        {
            Context.FinishSentryTransaction();
        }

        void Application_Error(object sender, EventArgs e)
        {
            var error = Server.GetLastError();
            var logDataService = ServiceLocator.Current.GetInstance<ILogDataService>();
            logDataService.LogEvent(new LogInfo("Global.asax Application_Error", error,
                HttpContext.Current.Request.Url.OriginalString));
        }

        void Application_OnPostAuthenticateRequest(object sender, EventArgs e)
        {
            var sessionManager = ServiceLocator.Current.GetInstance<IUserSessionManager>();
            var sessionValidationResult = sessionManager.ValidateUserSessionCookie(HttpContext.Current);
            if (!sessionValidationResult.IsSessionStarted)
            {
                return;
            }

            if (!sessionValidationResult.IsValid)
            {
                sessionManager.TerminateUserSession(HttpContext.Current, false);
                //Response.Redirect(Request.Url.ToString());
                Response.Redirect(FormsAuthentication.LoginUrl);
                return;
            }

            var currentUser = ServiceLocator.Current.GetInstance<IContextVariablesProvider>().GetUser();
            Context.User = currentUser; // Assigning the context user
        }


        private static void RegisterRoutes(RouteCollection routes)
        {
            routes.Add(new Route("Admin/InventoryItems/Images/{imageId}/{sizeId}/{imageName.jpg}",
                new InventoryImageRouteHandler()));
            routes.Add(new Route("Admin/InventoryItems/Images/{imageId}/{sizeId}",
               new InventoryImageRouteHandler()));
            routes.Add(new Route("Admin/RecyclingMasterItemImages/Images/{imageId}/{sizeId}",
                new RecyclingMasterItemImageRouteHandler()));
            routes.Add(new Route("Admin/RecyclingOrderItemImages/Images/{imageId}/{sizeId}",
                new RecyclingOrderItemImageRouteHandler()));
            routes.Add(new Route("Admin/InventoryItemImages/Images/{imageId}/{sizeId}",
                new InventoryItemImageRouteHandler()));
            routes.Add(new Route("TakePhoto", new TakeImageHandler()));
            routes.Add(new Route("TakePhotoInventory", new TakeImageInventoryHandler()));
            routes.Add(new Route("GetLogo/{logoType}", new LogoHandler()));
            routes.Add(new Route("setSignatureImage/{signatureId}", new SetSignatureImageRouteHandler()));
            routes.Add(new Route("SetDSVImage/{customerId}", new SetDSVImageRouteHandler()));

            routes.Add(new Route("Admin/ITad/{page}.aspx", new ITadRouteHandler()));
        }

        private static void EnsureConfiguration()
        {
            var sl = ServiceLocator.Current;
            try
            {
                var configSections = new IConfigSection[]
                {
                    sl.GetInstance<IReportEngineConfiguration>()
                }.ToList();

                configSections.ForEach(it => it.EnsureInitialized());
            }
            catch (Exception ex)
            {
                var logDataService = sl.GetInstance<ILogDataService>();
                logDataService.LogEvent(new LogInfo("Global.asax EnsureConfiguration()", ex, HttpContext.Current.Request.Url.OriginalString));
                throw new Exception("Fatal exception: failed to initialize the application - a vital resouce is unavailable.", ex);
            }
        }
    }
}
