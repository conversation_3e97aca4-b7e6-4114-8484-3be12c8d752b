using System;
using Razor.Common.Base.Context;
using Razor.Common.Base.Context.Data;
using Razor.Common.Core.Company.Data;
using Razor.Data.Core.DataServices.Customer;
using Razor.Data.Core.DataServices.Logo.Models;
using Razor.Data.Core.DataServices.User;
using Razor.Logic.Common.Helpers;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Impl.Reports.ArInvoice.Base.Model.Sections;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Mappers;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Model;
using Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Services.CrmAccount;

namespace Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Impl.Reports.Base
{
    public abstract class DataSourceBuilderBase<TRequestModel, TDataSource> : IDataSourceBuilder<TRequestModel, TDataSource>
        where TDataSource : DocumentDataSourceBase, new()
    {
        public virtual bool FillCommonCompanyInfo { set; get; } = true;

        public virtual TDataSource Build(TRequestModel rq)
        {
            var user = Context.GetUser();
            var ds = new TDataSource
            {
                OwnerCompany = new CompanyInfo(),
                UnitsAndCodes = new UnitsAndCodesSection
                {
                    WeightUoM = user.WeightMainLabel,
                    LengthUoM = user.LengthLabel,
                    CurrencyMark = CompanyDataService.GetHomeCurrency().GetDesignation().Trim(),
                    HomeCurrencyMark = CompanyDataService.GetHomeCurrency().GetDesignation().Trim()
                }
            };
            if (!FillCommonCompanyInfo)
            {
                return ds;
            }

            var timeZone = Context.TimeZone;
            var utcNow = DateTime.UtcNow;
            
            var domain = DsConfigDb.GetCompanyMainDomain(Context.ContextVariables.CompanyId);
            var baseUrl = $"https://{domain.DomainName}";

            ds.TimeZone = new TimeZoneSection
            {
                TimeZoneInfo = timeZone,
                TimeZoneName = timeZone.DisplayName,
                TimeZoneUtcOffsetMinutes = (int)timeZone.GetUtcOffset(utcNow).TotalMinutes,
                DaylightName = timeZone.DaylightName,
                IsUsingDaylightSavingTime = timeZone.IsDaylightSavingTime(utcNow),
                UtcNow = utcNow,
                Now = TimeZoneInfo.ConvertTimeFromUtc(utcNow, timeZone)
            };
            ds.Urls = new UrlsSection
            {
                BaseUrl = baseUrl,
                ReportsLogoUrl = LogoHelper.GetLogoUrl(baseUrl, LogoTypes.PdfHeader),
                MainLogoUrl = LogoHelper.GetLogoUrl(baseUrl, LogoTypes.Header),
                SignatureUrl = LogoHelper.GetLogoUrl(baseUrl, LogoTypes.Signature)
            };


            var selfCustomerId = CustomerDataService.GetSelfCustomerId();
            ds.OwnerCompany = CrmAccountReportService.GetCrmAccount<CompanyInfo>(selfCustomerId) ?? new CompanyInfo();

            var selfCustomerAddress = CustomerDataService.GetSelfCustomerAddress();
            ds.SelfCustomerAddress = AddressMapper.Map(selfCustomerAddress);
            ds.CurrentDateTimeString = UserDataService.GetUserCurrentDateTimeString(utcNow);

            return ds;
        }

        protected DataSourceBuilderBase(
            ILogoHelper logoHelper, 
            IUserDataService userDataService, 
            IContextVariablesProvider context, 
            ICustomerDataService customerDataService, 
            ICompanyDataService companyDataService, 
            IAddressMapper addressMapper, 
            ICrmAccountReportService crmAccountReportService, 
            IConfigDbContextDataService dsConfigDb)
        {
            LogoHelper              = logoHelper;
            UserDataService         = userDataService;
            Context                 = context;
            CustomerDataService     = customerDataService;
            CompanyDataService      = companyDataService;
            AddressMapper           = addressMapper;
            CrmAccountReportService = crmAccountReportService;
            DsConfigDb = dsConfigDb;
        }

        protected readonly ILogoHelper LogoHelper;
        protected readonly IUserDataService UserDataService;
        protected readonly ICustomerDataService CustomerDataService;
        protected readonly ICompanyDataService CompanyDataService;
        protected readonly IConfigDbContextDataService DsConfigDb;
        protected readonly IAddressMapper AddressMapper;
        protected readonly IContextVariablesProvider Context;
        protected readonly ICrmAccountReportService CrmAccountReportService;
    }
}