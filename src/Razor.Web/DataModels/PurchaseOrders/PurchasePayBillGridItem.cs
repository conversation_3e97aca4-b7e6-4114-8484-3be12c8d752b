using System;
using Razor.Common.Base.Attributes;

namespace RazorWeb.DataModels.PurchaseOrders
{
    public class PurchasePayBillGridItem
    {
        [ColumnCaption(Caption = "Invoice #", Order = 1)]
        public Int64 InvoiceId { get; set; }

        [ColumnCaption(Caption = "Due Date", Order = 2)]
        public DateTime DueDate { get; set; }

        [ColumnCaption(Caption = "Vendor Name", Order = 3)]
        public String VendorName { get; set; }

        [ColumnCaption(IsHidden = true)]
        public Int64 CustomerId { get; set; }

        [ColumnCaption(Caption = "Reference #", Order = 4)]
        public String Reference { get; set; }

        [ColumnCaption(Caption = "Amount Due", Order = 5)]
        public Decimal AmountDue { get; set; }

        [ColumnCaption(Caption = "Discount Used", Order = 6)]
        public Decimal Discount { get; set; }

        [ColumnCaption(Caption = "Credit Used", Order = 7)]
        public Decimal Credit { get; set; }

        [ColumnCaption(Caption = "Amount To Pay", Order = 8)]
        public Decimal AmountToPay { get; set; }

        [ColumnCaption(IsHidden = true)]
        public String CheckNo { get; set; }
    }
}