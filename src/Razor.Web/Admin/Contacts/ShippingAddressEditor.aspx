<%@ Page Language="C#" 
    AutoEventWireup="true" 
    CodeBehind="ShippingAddressEditor.aspx.cs" 
    Inherits="RazorWeb.Admin.Contacts.ShippingAddressEditor" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
</head>
<body>
    <div class="editEmailPopup clearfix">

        <div class="editEmailPopupItem clearfix">
            <span>First name:</span>
            <input type="text" id="tbFirstName" class=""/>
        </div>

        <div class="editEmailPopupItem clearfix">
            <span>Middle initial:</span>
            <input type="text" id="tbMiddleName" class=""/>
        </div>

        <div class="editEmailPopupItem clearfix">
            <span>Last name:</span>
            <input type="text" id="tbLastName" class=""/>
        </div>

        <div class="editEmailPopupItem clearfix">
            <span>Company:</span>
            <input type="text" id="tbCompany" class=""/>
        </div>

        <div class="editEmailPopupItem clearfix">
            <span>Phone:</span>
            <input type="text" id="tbPhone" class=""/>
        </div>

        <div class="editEmailPopupItem clearfix">
            <span>Postal Code:</span>
            <input type="text" id="tbPostalCode" class=""/>
        </div>

        <div class="editEmailPopupItem clearfix">
            <span>Country Code:</span>
            <input type="text" id="tbCountry" class=""/>
        </div>

        <div class="editEmailPopupItem clearfix">
            <span>State Code:</span>
            <input type="text" id="tbState" class="wide-input"/>
            <input type="text" id="tbStateList" class="" style="display: none;"/>
        </div>

        <div class="editEmailPopupItem clearfix">
            <span>City:</span>
            <input type="text" id="tbCity" class=""/>
        </div>

        <div class="editEmailPopupItem clearfix">
            <span>Street Address 1:</span>
            <input type="text" id="tbAddress1" class=""/>
        </div>

        <div class="editEmailPopupItem clearfix">
            <span>Street Address 2:</span>
            <input type="text" id="tbAddress2" class=""/>
        </div>

        <div class="editEmailPopupItem clearfix">
            <span>Street Address 3:</span>
            <input type="text" id="tbAddress3" class=""/>
        </div>

        <div class="editEmailPopupItem clearfix">
            <span>Reference Number:</span>
            <input type="text" id="tbReferenceNumber" class=""/>
        </div>

    </div>
</body>
</html>
