using System;
using Razor.Common.Base.Attributes;
using Razor.Tools.DataAccess.DataServiceBase.Convertable.Attributes;

namespace Razor.Data.Core.DataServices.RecyclingOrder.Models
{
    [Convertible]
    public class BillOfLadingItem
    {
        [DbColumnMapping(SourceColumn = "PACKAGING_TYPE_ID")]
        public Int64 PackagingTypeId { set; get; }

        [DbColumnMapping(SourceColumn = "PACKAGING_TYPE_DESC")]
        public String PackagingTypeCd { set; get; }

        [DbColumnMapping(SourceColumn = "ITEM_COUNT")]
        public Int32 ItemCount { set; get; }

        [WeightConvertion]
        [DbColumnMapping(SourceColumn = "WEIGHT")]
        public Double Weight { set; get; }
    }
}