using System;
using Razor.Common.Base.Attributes;

namespace Razor.Data.Core.DataServices.Category.Models
{
    public class InventoryCategory
    {
        [DbColumnMapping(SourceColumn = "CATEGORY_ID")]
        public Int64 Id { set; get; }

        [DbColumnMapping(SourceColumn = "PARENT_ID")]
        public Int64 ParentId { set; get; }

        [DbColumnMapping(SourceColumn = "INVENTORY_ATTRIBUTE_TYPE_ID")]
        public Int32 TypeId { set; get; }

        [DbColumnMapping(SourceColumn = "INVENTORY_ATTRIBUTE_NAME")]
        public String TypeName { set; get; }

        [DbColumnMapping(SourceColumn = "CATEGORY_DISPLAY_NAME")]
        public String Caption { set; get; }

        [DbColumnMapping(SourceColumn = "ITEM_CATEGORY_FULL_PATH")]
        public String FullPath { set; get; }

        [DbColumnMapping(SourceColumn = "HAS_CHILD_CATEGORIES")]
        public Boolean HasChildCategories { set; get; }

        [DbColumnMapping(SourceColumn = "EBAY_MAPPED_COUNT")]
        public Int32 MappedCategoriesCount { get; set; }

        [DbColumnMapping(SourceColumn = "IS_PRIMARY")]
        public Boolean IsPrimary { get; set; }
    }
}