using System;

namespace Razor.Data.Core.DataServices.Tax
{
    public class TaxGridItem
    {
        public Int64? TaxId { get; set; }
        public String Tax { get; set; }
        public String TaxDescription { get; set; }
        public Double? TaxRate { get; set; }
        public Boolean IsInactive { get; set; }
        public String TaxAgencyCD { get; set; }
        public Boolean IsGroup { get; set; }
        public Int64? TaxAgency { get; set; }
        public bool IsEditable { get; set; }
    }
}