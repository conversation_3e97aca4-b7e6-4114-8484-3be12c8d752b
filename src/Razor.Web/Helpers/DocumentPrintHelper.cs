using System;
using System.IO;
using System.Linq;
using System.Management;
using System.Net.Sockets;
using System.Text;
using CommonServiceLocator;
using Razor.Common.Base.Enums;
using Razor.Common.Base.Helpers;
using Razor.Common.Core.Company.FileStorage;
using Razor.Common.Core.Company.FileStorage.Components;
using Razor.Data.Core;
using Razor.Data.Core.DataServices.Printer;
using Razor.Data.Core.DataServices.Remote.Printing.Models;
using Razor.Data.LegacyTableAdapters.DataAdapters.DsConfigTableAdapters;
using Razor.Tools.DataAccess;
using Razor.Tools.DataAccess.Helpers;


namespace RazorWeb.Helpers
{
    public class DocumentPrintHelper// : PdfPrintHelperBase
    {
        
        public static void PrintFileBytes(Byte[] data, String printerName, String fileName = null, String extension = null, PrinterJobType printerJobType = PrinterJobType.None)
        {
            PrintBase64String(Convert.ToBase64String(data), printerName, fileName, extension, printerJobType);
        }


        public static void PrintBase64String(String base64, String printerName, String fileName = null, String extension = null, PrinterJobType printerJobType = PrinterJobType.None)
        {
            using (var ta = TableAdapterFactory.CreateAdapter<ConfigQueriesTableAdapter>(ConnectionsStub.Company()))
            {
                ta.sp_SET_REMOTE_PRINTER_JOB(printerName, base64,
                    printerJobType == PrinterJobType.None
                        ? (Int32)PrinterJobType.Pdf
                        : (Int32)printerJobType, fileName);
            }
        }


        public static String PrintFromZebraCodes(String base64, String printerName)
        {
            try
            {
                using (var ta = TableAdapterFactory.CreateAdapter<ConfigQueriesTableAdapter>(ConnectionsStub.Company()))
                {
                    ta.sp_SET_REMOTE_PRINTER_JOB(printerName, base64, (Int32)PrinterJobType.ZebraCodes, null);
                }

                return null;
            }
            catch (Exception ex)
            {
                return ex.Message;
            }                        
        }

        private static String GetServerPrinterIpByName(String printerName)
        {
            const string result = "Unknown";
            try
            {
                var query = string.Format("SELECT * from Win32_Printer WHERE Name LIKE '%{0}%'", printerName);
                var searcher = new ManagementObjectSearcher(query);
                var coll = searcher.Get();

                foreach (var printer in coll)
                {
                    foreach (PropertyData property in printer.Properties)
                    {
                        if (property.Name == "PortName")
                        {
                            return property.Value.ToString();
                        }
                    }
                }
            }
            catch (Exception)
            {

            }
            return result;
        }   

        // delete server name from printer
        private static String NormalizePrinterName(String printerName)
        {
            if (printerName.Contains("\\"))
            {
                return printerName.Substring(printerName.LastIndexOf("\\", StringComparison.Ordinal) + 1,
                                             printerName.Length - 1 -
                                             printerName.LastIndexOf("\\", StringComparison.Ordinal));
            }
            return printerName;
        }

        private const String _defaultFileExtension = ".pdf";
        private PrinterJobType _defaultPrinterJobType = PrinterJobType.Pdf;
    }

    //public class ExcelPrintHelperBase { 
    
    //}
}