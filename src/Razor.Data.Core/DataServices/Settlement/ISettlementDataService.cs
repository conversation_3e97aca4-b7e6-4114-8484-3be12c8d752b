using System;
using System.Collections.Generic;
using Razor.Common.Base.CommonModels.Pagination;
using Razor.Data.Core.DataServices.Invoice.Model.Void;
using Razor.Data.Core.DataServices.Recycling.Models;
using Razor.Data.Core.DataServices.RecyclingOrder.Models;
using Razor.Data.Core.DataServices.RecyclingOrder.Models.Audit;
using Razor.Data.Core.DataServices.RecyclingOrder.Models.Settlement;
using Razor.Data.Core.DataServices.RecyclingOrder.Models.Settlement.Services;
using Razor.Tools.DataAccess.DataServiceBase;

namespace Razor.Data.Core.DataServices.Settlement
{
    public interface ISettlementDataService : IBaseRepository
    {
        RecyclingOrderAuditRow[] GetRecyclingOrderAudits(long recyclingOrderId, int pageIndex, int itemsPerPage, string orderColumn,
            string orderDirection, string filterWhere, long? parentId, out long totalRows);

        PageOfRows<SettlementOrderItemGridItem> GetRecyclingOrderItemsToSettle(long recyclingOrderId, int settlementStateId,
            bool viewSummary, string sortColumn, string sortOrder, int itemsPerPage, int pageIndex);

        PageOfRows<SettlementOrderItemGridItem> GetRecyclingOrderItemsToSettleInitialDetial(long recyclingOrderId, long? parentId,
            string sortColumn, string sortOrder, int itemsPerPage, int pageIndex);

        PageOfRows<SettlementOrderItemGridItem> GetRecyclingOrderItemsToSettleInitialDetialExport(long recyclingOrderId,
            string sortColumn, string sortOrder, int itemsPerPage, int pageIndex);

        IEnumerable<SettlementOutboundItemGridItem> GetRecyclingOrderOutboundAggregateItemsToSettle(long recyclingOrderId,
            string sortColumn, string sortOrder, int itemsPerPage, int pageIndex, string filters, out int totalRecords);

        IEnumerable<SettlementOutboundItemGridItem> GetRecyclingOutboundOrderItemsToSettle(long recyclingOrderId, string sortColumn,
            string sortOrder, int itemsPerPage, int pageIndex, string filters);

        PageOfRows<SettlementGridItem> GetReyclingOrdersToSettle(IPageRequest request, long[] customerIds, DateTime? startDate,
            DateTime? endDate, int statusIds, long[] warehouseId, string filters);

        /// <summary>
        /// Indicates if a RecylingOrder has SOs/POs with paid invoices
        /// </summary>
        InvoicesVoidableStateModel[] CheckSettlementOrderInvoicePaid(long recyclingOrderId);

        BooleanLookup[] SetRecyclingOrderItemSettlementMode(long recyclingOrderItemId);

        AssetGrading[] GetAssetsDevaluations(long[] assetIds);

        AssetGrading[] GetAssetsAllDevaluations(long recyclingOrderId);

        IEnumerable<SettlementOrderServiceReportModel> GetSettlementServices(long recyclingOrderId);

        PageOfRows<SettlementOrderServiceGridItem> GetSettlementServices(long recyclingOrderId, bool hideZeroPriceServices, IPageRequest request, string dbFilters);

        SettlementOrderServiceGridItem GetSettlementService(long recyclingOrderId, int serviceItemId);

        IEnumerable<SettlementOrderOnsiteServiceDetail> GetSettlementServicesDetails(long recyclingOrderId, int serviceTypeId);

        void UpdateOnsiteServiceCategory(Int64? id, int estimatedQty);

        void UpdateOnsiteServiceLabel(Int64? id, int estimatedQty);

        void RecalculateInboundOrderServicePricing(Int64? recyclingOrderId, Int64[] serviceTypeIds);

        void SaveAssetSettlementCosts(long? id, decimal? dataErasureCost, decimal? auditCost, decimal? dataDestructionCost,
            decimal? miscOverhead, decimal? internalCost, decimal? shippingCost, decimal? adjustmentFX);

        AssetSettlementCostModel[] GetAssetSettlementCosts(long recyclingOrderId, int pageIndex, int itemsPerPage,
            string orderColumn, string orderDirection, string filterWhere, out long totalRows);


        /// <summary>
        /// Set the settlement state for recycling order
        /// </summary>
        /// <param name="recyclingOrderId"></param>
        /// <param name="settlementStateId"></param>
        void SetSettlementState(long recyclingOrderId, int settlementStateId);

        (long? serviceItemId, string warningMsg) SetRecyclingOrderItemService(SettlementOrderService model);

        decimal? GetServicePriceByQty(SettlementOrderService model);
    }
}
