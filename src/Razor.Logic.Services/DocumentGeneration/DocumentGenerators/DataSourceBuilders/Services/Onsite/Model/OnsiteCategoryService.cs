using Newtonsoft.Json;

namespace Razor.Logic.Services.DocumentGeneration.DocumentGenerators.DataSourceBuilders.Services.Onsite.Model
{
    public class OnsiteCategoryService
    {
        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        [JsonProperty(PropertyName = "Estimated Qty")]
        public int EstimatedQty { get; set; }

        [JsonProperty(PropertyName = "Onsite Qty")]
        public int OnsiteQty { get; set; }

        [JsonProperty(PropertyName = "Onsite TypeId")]
        public int OnsiteTypeId { get; set; }
    }
}
