using System;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation;
using Razor.Common.Base.Context;
using Razor.Logic.MqServices.BaseConsumers;
using Razor.Logic.MqServices.Printing.Reports.ReportGenerator.Services;
using Razor.Mq.Contracts.Printing.Reports.List;

namespace Razor.Logic.MqServices.Printing.Reports.ReportGenerator.Consumers
{
    internal class ReportsListConsumer : PayloadConsumer<ReportsListRequest, ReportsList, ReportsListResponse, ReportsListed>
    {
        public ReportsListConsumer(Lazy<IReportsMicroService> service, IContextVariablesProvider cvp, IValidator<ReportsList> validator) : base(cvp, validator)
        {
            _service = service;
        }

        protected override Task<ReportsListed> ProduceResponsePayload(ReportsList requestPlayload, CancellationToken cancellationToken) => 
            _service.Value.List(requestPlayload);

        private readonly Lazy<IReportsMicroService> _service;
    }
}
