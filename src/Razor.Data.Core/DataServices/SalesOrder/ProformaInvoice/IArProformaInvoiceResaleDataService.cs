using Razor.Data.Core.CommonDataModels;
using Razor.Data.Core.DataServices.Invoice.Base;
using Razor.Data.Core.DataServices.SalesOrder.Models.Invoice;
using Razor.Tools.DataAccess.DataServiceBase;

namespace Razor.Data.Core.DataServices.SalesOrder.ProformaInvoice
{
    public interface IArProformaInvoiceResaleDataService : IInvoiceReportDataService, IBaseRepository
    {
        IdName SetSalesOrderProformaInvoice(long? salesOrderId = null);
        InvoiceMainData GetSalesOrderProformaInvoice(long? salesOrderId = null, long? invoiceId = null);
    }
}