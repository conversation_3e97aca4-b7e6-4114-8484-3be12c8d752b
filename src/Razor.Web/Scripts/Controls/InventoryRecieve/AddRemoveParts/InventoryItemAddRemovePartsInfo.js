function InventoryItemAddRemovePartsInfo() {
    this.onCreate();
};

InventoryItemAddRemovePartsInfo.prototype = {
    _sel: {
        tbModel:                '#tb_InventoryItemAddRemovePartsInfo_Model',
        tbMfg:                  '#tb_InventoryItemAddRemovePartsInfo_Mfg',
        tbSerial:               '#tb_InventoryItemAddRemovePartsInfo_Serial',
        tbCondition:            '#tb_InventoryItemAddRemovePartsInfo_Condition',
        tbCost:                 '#tb_InventoryItemAddRemovePartsInfo_Cost',
        tbPOCost:               '#tb_InventoryItemAddRemovePartsInfo_PO_Cost',
        tbRecomCost:            '#tb_InventoryItemAddRemovePartsInfo_RecommendedCost',
        tbPo:                   '#tb_InventoryItemAddRemovePartsInfo_PO'
    },

    _urls: {
        getPartPrice: Common.baseUrl + '#'
    },

    _data: {
        id: 0,
        row: null,
        recomCost: 0,
        POCost: 0
    },

    onCreate: function () {
        var it = this;
    },

    SetData: function (row) {
        var it = this;

        it._data.id = row.Id;
        it._data.row = row;
        it._data.recomCost = Number.parse(row.RecvPrice);
        it._data.POCost = Number.parse(row.PurchaseOrderPrice);

        $(it._sel.tbModel).val(row.MasterItemNumber);
        $(it._sel.tbMfg).val(row.MFGCd);
        $(it._sel.tbSerial).val(row.Serial);
        $(it._sel.tbCondition).val(row.RealCondition);
        $(it._sel.tbCost).val(row.RecvPrice);
        $(it._sel.tbPOCost).val(it._data.POCost);
        $(it._sel.tbRecomCost).val(it._data.recomCost);
        $(it._sel.tbPo).html(row.PurchaseOrderName);
    },

    OnAddedPart: function (dCost) {
        var it = this;
        
        it._data.recomCost += Number.parse(dCost);
        $(it._sel.tbCost).val(it._data.recomCost);
    },

    PriceCorrection: function(price) {
        var it = this;

        $(it._sel.tbCost).val(Number.toFixed(Number.parse(it._data.recomCost) - Number.parse(price), 2, true));
    }
};
