function SearchInventoryItem(term, onFound, onClose) {
    if (typeof(onFound) !== "function") {
        throw "Callback function is missing";
    }
    var idGrid = "gSearch";
    var idPager = "pSearch";
    
    var html = String.format(
        '<div class="table-wr" style="margin: 14px 10px 10px 3px">' +
        '   <table id="{0}"></table>' +
        '   <div id="{1}"></div>' +
        '</div>',
        idGrid,
        idPager);
    
    var grid = null;
    ShowMModalDialog(html, {
            title: "Scan to relocate",
            width: 802,
            height: 600,
            position: "center",
            modal: true,
            resizable: true,
            buttons: {
                "OK": function(event) {
                    var $dlg = $(this);
                    var selRowIds = grid.getGridParam("selarrrow");
                    var itemIds = _.map(selRowIds, function(rowId) {
                        var item = grid.getRowData(rowId);
                        return Number(item.ItemInventoryId);
                    });
                    onFound(term, itemIds);
                    $dlg.dialog("close");
                },
                "Close": function (event) {
                    if (typeof(onFound) === "function") {
                        onClose();
                    }
                    $(this).dialog("close");
                }
            }
        },
        // onShow
        function() {
            var selGrid = idSelector(idGrid);
            var selPager = idSelector(idPager);
            grid = $(selGrid)
                .jqGrid({
                    pager: selPager,
                    url: Common.baseUrl + '/Services/InventoryAuditService.asmx/LoadSearchItemGrid',
                    type: "POST",
                    datatype: "json",
                    ajaxGridOptions: { contentType: "application/json; charset=utf-8" },
                    jsonReader: {
                        root: "d.rows",
                        id: "SalesOrderId",
                        page: "d.page",
                        total: "d.total",
                        records: "d.records",
                        repeatitems: false
                    },
                    prmNames: { nd: null, page: "page", rows: "rows" },
                    serializeGridData: function(postData) {
                        postData.Data = term;
                        return "request=" + encodeURIComponent(JSON.stringify(postData));
                    },
                    shrinkToFit: true,
                    width: 770,
                    height: 390,
                    rowNum: CommonGridParams.DefaultRows,
                    rowList: CommonGridParams.RowList,
                    colNames: ["ItemInventoryId", "Model", "Serial", "Unique Identifier", "Manufacturer", "Location", "Condition"],
                    colModel: [
                        { name: "ItemInventoryId",  hidden: true, editable: false },
                        { name: "Model",            width: 90, editable: false, search: true },
                        { name: "Serial",           width: 120, editable: false, search: true },
                        { name: "UniqueIdentifier", width: 150, editable: false, search: true },
                        { name: "Manufacturer",     width: 120, editable: false, search: true },
                        { name: "Location",         width: 80, editable: false, search: true },
                        { name: "Condition",        width: 100, editable: false, search: true }
                    ],
                    sortname: "ItemInventoryId",
                    sortorder: "asc",
                    multiselect: true,
                    multiboxonly: false,
                    multiselectWidth: 30,
                    gridview: true,

                    viewrecords: true,
                    altRows: true,
                    altclass: "grey_td",
                    _selectedRowId: -1,
                    beforeSelectRow: handleJqGridMultiSelect
                })
                .navGrid(selPager,
                    {
                        add: false,
                        edit: false,
                        del: false,
                        refresh: true,
                        search: false
                    });
        },
        // onHide
        function() {

        });
};