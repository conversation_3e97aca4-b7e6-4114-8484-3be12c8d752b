/*
Plugin for both
    <input  id="some_id" type="text" value=""/>
    <label for="some_id" id="some_label_id">Pay/Charge<label>

    or

    <span  id="some_id"><span>
    <label for="some_id" id="some_label_id">Pay/Charge<label>

Can be created with $(label or span/input selector).LabelPayCharge(); // will bind onChange event
Can be invoked with 
    $(label or span/input selector).labelPayChargeRefresh();          // will refresh the pair's view
    $(label or span/input selector).labelPayChargeGetValue();         // will return the numeric value of the input
*/

(function ($) {
    var dataKey       = 'LabelPayCharge';
    var defOptions = {
        // classes to be applied by the input/span value
        classNegative: 'cost-negative',
        classZero:     'cost-zero',
        classPositive: null,
        // ---
        precesion: 2,   // decimal places
        keepTrailingZeros: true,
        mark: UserSettings.GetCurrencyMark(),      // mark near the input/span value
        isAhead: true,  // place the mark ahead of the input/span value
        // text of the label to be applied by the input/span value
        pay: 'Pay',
        charge: 'Charge',
        zero: '–',
        keepLabel: false, // keep the initial label text despite the value
        writeZeroToLabel: true, // change label text to "options.zero" if the value of input/span == '0'
        // ---
        hideLabelIfZero: false // label will be hidden if the value of input/span == '0',
    };

    // Refreshes appearance by the provided value
    $.fn.payChargeToggleByValue = function(value) {
        var pair = {
            $input: null,
            $label: $(this)
        };
        toggleDom(pair, value);
    };

    // Refreshes input/span-label pair state by the existing value
    $.fn.payChargeRefresh = function(containsRealValue) {
        var pair = getPair(this),
            value = getInputValue(pair.$input, containsRealValue);
        toggleDom(pair, value);
        setInputValue(pair.$input, value);
    };

    // Sets the value of the input/span
    $.fn.payChargeSetValue = function(value, mark, wrapper = "{0}") {
        var pair = getPair(this);
        value = Number.parse(value);
        toggleDom(pair, value);
        setInputValue(pair.$input, value, mark, wrapper);
    };

    $.fn.toFixedForPrecision = function (number) {
        var pair = getPair(this);
        number = Number.parse(number);
        toggleDom(pair, number);
       
        var options = storage(pair.$input).options;

        number = toFixedForPrecision(number, options.precesion, options.keepTrailingZeros);
        pair.$input.text(number);
    };

    // Will return the value of the input/span
    $.fn.payChargeGetValue = function () {
        var pair = getPair(this);
        return getInputValue(pair.$input);
    };
    
    // Constructor
    $.fn.LabelPayCharge = function (options) {
        var $all = $(this);
        $all.each(function (i, dom) {
            var pair = getPair(dom);
            if (!exists(pair.$input)) {
                pair.$input.on('change', function () {
                    $(dom).payChargeRefresh(true);
                });
            }

            var it = storage(pair.$input);           

            it.options = $.extend({}, defOptions, options);
            if (it.options.keepLabel)
            {
                var name = $(pair.$label).text();// read as text not to be html-encoded
                $.extend(it.options, {
                    pay:    name,
                    charge: name,
                    writeZeroToLabel: false
                });
            }

            pair.$input.payChargeRefresh(true);
        });

        return $all;
    };

    // gets <input/span, label> pair
    function getPair(sel) {
        var $sel = $(sel);
        if ($sel.length == 0) {
            throw 'Error: Empty set';
        }

        if ($sel.is('label')) {
            return {
                $label: $sel,
                $input: $('#' + $sel.prop('for'))
            };
        } else {
            return {
                $input: $sel,
                $label: $sel.getLabels().first()
            };
        }
    }

    // toggles the classes and visibility of DOM elements
    function toggleDom(pair, value) {
        function toggleClassByName($dom, name, classesToRemove) {
            if ($dom && $dom.length > 0) {
                $(classesToRemove).each(function(i, cName) {
                    if (!String.isNullOrWhiteSpace(cName)) {
                        $dom.toggleClass(cName, false);
                    }
                });

                if (!String.isNullOrWhiteSpace(name)) {
                    $dom.toggleClass(name, true);
                }
            }
        }

        var options = pair.$input && exists(pair.$input)
            ? storage(pair.$input).options
            : defOptions;
        
        // classes
        var className = 
            value == 0 ? options.classZero : 
            value > 0 ? options.classPositive : 
            options.classNegative;
        var toRemove = value == 0 ? [options.classNegative, options.classPositive] :
            value > 0 ? [options.classNegative, options.classZero] :
            [options.classZero, options.classPositive];

        toggleClassByName(pair.$label, className, toRemove);
        toggleClassByName(pair.$input, className, toRemove);

        // label visibility and text
        var hideLabel = value == 0 && options.hideLabelIfZero;
        var zeroText = options.writeZeroToLabel ? options.zero : pair.$label.text(); // read as text not to be html-encoded
        var labelText = value == 0 ? zeroText :
            value > 0 ? options.pay : 
            options.charge;
        if (pair.$label && pair.$label.length > 0) {
            if (pair.$label.is(':visible') && hideLabel) {
                pair.$label.hide();
            } else if (pair.$label.is(':hidden') && !hideLabel) {
                pair.$label.show();
            }
            
            pair.$label.html(labelText);
        }
    }

    // returns the value of $input
    function getInputValue($input, containsRealValue) {
        // extract the value
        var value;
        if ($input.is('input')) {
            value = $input.val();
        } else {
            value = UserSettings.UnformatCurrency($input);
        }

        // transform to negative if needed
        value = Number.parse(value);
        if (!containsRealValue) {
            var options = storage($input).options;
            if ($input.hasClass(options.classNegative)) {
                value = -1 * value;
            }
        }
        return value;
    }
    
    function toFixedForPrecision(number, precesion = 2, keepTrailingZeros = true) {
        var x = Math.pow(10, Number(precesion) + 1);
        return (Number(number) + Math.sign(number) * (1 / x)).toFixed(precesion, keepTrailingZeros, true);
    }

    // sets the value of $input
    function setInputValue($input, number, mark, wrapper) {
        number = Number.parse(number);
        var options = storage($input).options;

        // input - output the number as it is
        if ($input.is('input')) {
            number = toFixedForPrecision(number, options.precesion, options.keepTrailingZeros);//Number.toFixed(number, options.precesion, options.keepTrailingZeros, true);
            $input.val(number);
            return;
        }
        
        // get the absolute value replacing 0 from options.zero
        var abs = Math.abs(number);
        if (abs == 0) {
            abs = options.zero;
        } else {
            abs = toFixedForPrecision(abs, options.precesion, options.keepTrailingZeros);//Number.toFixed(abs, options.precesion, options.keepTrailingZeros, true);
        }

        if (mark && $.isFunction(mark)) {
            mark = mark();
        }
        var markOverride = options.withoutMark || abs == options.zero || String.isNullOrWhiteSpace(options.mark)
            ? ''
            : (mark ? mark : options.mark);

        var html = UserSettings.FormatCurrency(abs, !options.isAhead, markOverride); // override can be html    
        if (wrapper) {
            html = String.format(wrapper, html)
        }
        $input.html(html);
    }

    // Checks that the plugin is already applied
    function exists($input) {
        var data = $input.data(dataKey);
        return (typeof (data) !== "undefined" && data != null);
    }

    // Returns permanent storage object
    function storage($input) {
        var data = $input.data(dataKey);
        if (!data) {
            data = {
                options: $.extend({}, defOptions)
            };
            $input.data(dataKey, data);
        }
        return data;
    };
}(jQuery));
