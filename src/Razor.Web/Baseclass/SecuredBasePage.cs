using System;
using System.Linq;
using System.Web;
using CommonServiceLocator;
using Razor.Common.Base.Context;
using Razor.Common.Base.Context.Models;
using Razor.Common.Base.Helpers;
using Razor.Common.Core.UserAccess.Data.Models;
using Razor.Common.Core.UserAccess.Enums;
using Razor.Data.Core.DataServices.System;
using System.Web.UI.WebControls;
using Razor.Common.Web.UserAccess.PageAccess;


namespace RazorWeb.Baseclass
{
    //[RequiresHttps(Severity = HttpsRequirementSeverity.DontCare)]
    public class SecuredBasePage : WebPageBase, IContextVariablesDrivenControl
    {
        public static bool RedirectToAccessDenied(HttpContext context, string reason = null, string details = null, bool canRetry = true)
        {
            if (context.Request.Url.AbsoluteUri.Contains("/Account/"))
                return false;

            if (string.IsNullOrWhiteSpace(reason))
                reason = "Sorry, you have no permission to view the requested page.";

            var path = $"~/Account/AccessDenied.aspx?ReturnUrl={HttpUtility.UrlEncode(context.Request.Url.LocalPath)}&reason={HttpUtility.UrlEncode(reason)}&canRetry={canRetry}";
            if (!string.IsNullOrWhiteSpace(details))
                path = $"{path}&details={details}";

            context.Response.Redirect(path);
            return true;
        }

        public IContextVariablesProvider ContextVariablesProvider { set; get; }

        public IUserAccessFilterService UserAccessFilterService => _userAccessFilterService ??= GetService<IUserAccessFilterService>();

        private IUserAccessFilterService _userAccessFilterService = null;

        public long CompanyId { set; get; }

        #region Public Constructors

        /// <summary>
        /// Constructor. Sets protection level to User by default.
        /// </summary>
        public SecuredBasePage()
        {
            ProtectionLevel = SystemAccessLevels.User;

            ContextVariablesProvider = GetService<IContextVariablesProvider>();
            CompanyId = ContextVariablesProvider.ContextVariables.CompanyId;
        }

        #endregion

        #region Public Properties

        public UserPagePermissions PagePermissions { get; set; }

        protected OperatedEntityTypes[] PageControlsOperatedEntities = Array.Empty<OperatedEntityTypes>();
        public EntityActions[] ActionPermissions
        {
            get
            {
                _actionPermissions ??= UserAccessFilterService
                    .GetUserEntityActionPermissions(PageControlsOperatedEntities)
                    .ToArray();
                return _actionPermissions;
            }
        }

        private EntityActions[] _actionPermissions;

        #endregion

        #region Protected Methods

        protected static TService GetService<TService>() => ServiceLocator.Current.GetInstance<TService>();

        /// <summary>
        /// Occures on each prerender of the page. Checks is user have or have not access to the web page.
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {
            var result = UserAccessFilterService.IsAccessToSectionAllowed(FunctionalCategory, FunctionalSectionId, ProtectionLevel, adminPageId: AdminPageId);
            PagePermissions = result.Result.PagePermissions;
            if (result.Result.AuthorizeResult != AuthorizeResult.Ok)
            {
                RedirectToAccessDenied(Context, result.Message);
                return;
            }

            if (Master != null)
            {
                if (Master.FindControl("hf_PagePermissions") is HiddenField hfPagePermissions)
                    hfPagePermissions.Value = PagePermissions.SerializeToJson();

                if (Master.FindControl("hf_ActionPermissions") is HiddenField hfActionPermissions)
                    hfActionPermissions.Value = ActionPermissions.Select(it => (int)it).SerializeToJson();
            }
            LoadPageTitle();
        }

        private void LoadPageTitle()
        {
            var pageName = GetService<ISystemSettingDataService>().GetPageName(FunctionalSectionId);
            if (!string.IsNullOrWhiteSpace(pageName))
                Page.Title = pageName;
        }


        /// <summary>
        /// protection level for this page.
        /// </summary>
        protected SystemAccessLevels ProtectionLevel { get; set; }
        protected SettingsPages AdminPageId = SettingsPages.Undefined;
        protected FunctionalCategories FunctionalCategory = FunctionalCategories.Undefined;
        protected FunctionalSections FunctionalSectionId = FunctionalSections.Undefined;
        #endregion
    }
}