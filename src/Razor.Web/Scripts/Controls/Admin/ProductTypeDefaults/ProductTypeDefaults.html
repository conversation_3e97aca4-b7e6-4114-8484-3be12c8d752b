<style type="text/css">
    .state-green {
        color: green;
    }

    .state-gray {
        color: inherit;
    }

    .state-red {
        color: maroon;
    }
    .cqo-label {
        padding-right: 10px;
        box-sizing: border-box;
    }

    .content-horiz-item table {
        table-layout: fixed;
    }
    .content-horiz-item table td {
        vertical-align: top;
    }
    fieldset {
        border: none;
    }
    
    select.checkbox-select {
        width: 78px;
    }

    
    select.checkbox-select.yes-no-select {
        width: 46px;
    }

    h4>label {
        width: 120px !important;
        margin-top:2px;
    }

    h4.item-caption {
        padding-bottom: 0 !important;
    }
</style>


<div class="content-wrapper content clearfix">

    <div class="content-title clearfix">
        <h2 class="" style="float: left">Product Type Defaults</h2>
        <!--<button class="button btnSelectClass" type="button" id="btQbSave" on-click="onSave" style="float: left; margin: 0 0 0 20px;">Save</button>-->
    </div>

    <div class="content-data clearfix">


		<div class="content-vertic-item clearfix">


			{{#with model.Defaults[0]}}
			<div class="content-vertic-item clearfix">
				<h4 class="item-caption">
					<label for="inventory_disabled" class="cqo-label">Master Item</label>
				</h4>
				<select id="inventory_disabled" value="{{this.IsEnabled}}" class="checkbox-select">
					<option value="{{toBoolean("false")}}">Disabled</option>
					<option value="{{toBoolean("true")}}">Enabled</option>
				</select>
			</div>
			<div class="content-vertic-item-data clearfix">
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label>Default Item Type</label>
					</div>
					<div class="content-horiz-item-row-right">
						<select value="{{this.DefaultItemTypeId}}" {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}>
							{{#each model.InternalItemTypes}}
							<option value="{{this.value}}">{{this.label}}</option>
							{{/each}}
						</select>
					</div>
				</div>
				<!--SALES-->
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label for="Inventory_SALES" class="cqo-label">Sales Account</label>
					</div>
					<div class="content-horiz-item-row-right">
						<input type="text" id="Inventory_SALES"
							   {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}
							   data-type-id="{{accounts.types.Income}}"
							   decorator="UnifiedAutocomplete:{{accounts.url}},{{accounts.options}},{{accounts.events}},true"
							   value="{{this.AccountSalesName}}"
							   autocomplete-item-value="{{this.AccountSalesId}}"
							   placeholder="{{getDefaultPlaceholder(model.DefaultAccounts, accounts.types.Income)}}"
							   class="cqo-input-text" />
					</div>
				</div>
				<!--EXPENSE-->
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label for="Inventory_EXPENSE" class="cqo-label">Expense Account</label>
					</div>
					<div class="content-horiz-item-row-right">
						<input type="text" id="Inventory_EXPENSE"
							   {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}
							   data-type-id="{{accounts.types.Expense}}"
							   decorator="UnifiedAutocomplete:{{accounts.url}},{{accounts.options}},{{accounts.events}},true"
							   value="{{this.AccountExpenseName}}"
							   autocomplete-item-value="{{this.AccountExpenseId}}"
							   placeholder="{{getDefaultPlaceholder(model.DefaultAccounts, accounts.types.Expense)}}"
							   class="cqo-input-text" />
					</div>
				</div>
				<!--COGS-->
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label for="Inventory_COGS" class="cqo-label">COGS Account</label>
					</div>
					<div class="content-horiz-item-row-right">
						<input type="text" id="Inventory_COGS"
							   {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}
							   data-type-id="{{accounts.types.CostOfGoodsSold}}"
							   decorator="UnifiedAutocomplete:{{accounts.url}},{{accounts.options}},{{accounts.events}},true"
							   value="{{this.AccountCogsName}}"
							   autocomplete-item-value="{{this.AccountCogsId}}"
							   placeholder="{{getDefaultPlaceholder(model.DefaultAccounts, accounts.types.CostOfGoodsSold)}}"
							   class="cqo-input-text" />
					</div>
				</div>
				<!--ASSET-->
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label for="Inventory_ASSET" class="cqo-label">Asset Account</label>
					</div>
					<div class="content-horiz-item-row-right">
						<input type="text" id="Inventory_ASSET"
							   {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}
							   data-type-id="{{accounts.types.OtherCurrentAsset}}"
							   decorator="UnifiedAutocomplete:{{accounts.url}},{{accounts.options}},{{accounts.events}},true"
							   value="{{this.AccountAssetName}}"
							   autocomplete-item-value="{{this.AccountAssetId}}"
							   placeholder="{{getDefaultPlaceholder(model.DefaultAccounts, accounts.types.OtherCurrentAsset)}}"
							   class="cqo-input-text" />
					</div>
				</div>
				<!--DISCOUNT-->
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label for="Inventory_DISCOUNT" class="cqo-label">Discount Account</label>
					</div>
					<div class="content-horiz-item-row-right">
						<input type="text" id="Inventory_DISCOUNT"
							   {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}
							   data-type-id="0"
							   decorator="UnifiedAutocomplete:{{accounts.url}},{{accounts.options}},{{accounts.events}},true"
							   value="{{this.AccountDiscountName}}"
							   autocomplete-item-value="{{this.AccountDiscountId}}"
							   class="cqo-input-text" />
					</div>
				</div>
				<!--SHIPPING COST-->
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label for="Inventory_SHIPPING" class="cqo-label">Shipping Cost</label>
					</div>
					<div class="content-horiz-item-row-right">
						<input type="text" id="Inventory_SHIPPING"
							   {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}
							   data-type-id="0"
							   decorator="UnifiedAutocomplete:{{accounts.url}},{{accounts.options}},{{accounts.events}},true"
							   value="{{this.AccountShippingName}}"
							   autocomplete-item-value="{{this.AccountShippingId}}"
							   class="cqo-input-text" />
					</div>
				</div>
			</div>
			{{/with}}

			{{#with model.Defaults[2]}}
			<div class="content-vertic-item-data clearfix">
				<h4 class="item-caption">
					<label for="service_disabled" class="cqo-label">Service</label>
				</h4>
				<select id="service_disabled" value="{{this.IsEnabled}}" class="checkbox-select">
					<option value="{{toBoolean("false")}}">Disabled</option>
					<option value="{{toBoolean("true")}}">Enabled</option>
				</select>
			</div>
			<div class="content-vertic-item-data clearfix">
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label>Default Item Type</label>
					</div>
					<div class="content-horiz-item-row-right">
						<select value="{{this.DefaultItemTypeId}}" {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}>
							{{#each model.InternalItemTypes}}
							<option value="{{this.value}}">{{this.label}}</option>
							{{/each}}
						</select>
					</div>
				</div>
				<!--EXPENSE-->
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label for="Service_EXPENSE" class="cqo-label">Expense Account</label>
					</div>
					<div class="content-horiz-item-row-right">
						<input type="text" id="Service_EXPENSE"
							   {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}
							   data-type-id="{{accounts.types.Expense}}"
							   decorator="UnifiedAutocomplete:{{accounts.url}},{{accounts.options}},{{accounts.events}},true"
							   value="{{this.AccountExpenseName}}"
							   autocomplete-item-value="{{this.AccountExpenseId}}"
							   placeholder="{{getDefaultPlaceholder(model.DefaultAccounts, accounts.types.Expense)}}"
							   class="cqo-input-text" />
					</div>
				</div>
				<!--SALES-->
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label for="Service_SALES" class="cqo-label">Sales Account</label>
					</div>
					<div class="content-horiz-item-row-right">
						<input type="text" id="Service_SALES"
							   {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}
							   data-type-id="{{accounts.types.Income}}"
							   decorator="UnifiedAutocomplete:{{accounts.url}},{{accounts.options}},{{accounts.events}},true"
							   value="{{this.AccountSalesName}}"
							   autocomplete-item-value="{{this.AccountSalesId}}"
							   placeholder="{{getDefaultPlaceholder(model.DefaultAccounts, accounts.types.Income)}}"
							   class="cqo-input-text" />
					</div>
				</div>

			</div>
			{{/with}}
			{{#with model.Defaults[1]}}
			<div class="content-vertic-item clearfix">
				<h4 class="item-caption">
					<label for="non_inventory_disabled" class="cqo-label">Commodity</label>
				</h4>
				<select id="non_inventory_disabled" value="{{this.IsEnabled}}" class="checkbox-select">
					<option value="{{toBoolean("false")}}">Disabled</option>
					<option value="{{toBoolean("true")}}">Enabled</option>
				</select>
			</div>
			<div class="content-vertic-item-data clearfix">
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label>Default Item Type</label>
					</div>
					<div class="content-horiz-item-row-right">
						<select value="{{this.DefaultItemTypeId}}" {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}>
							{{#each model.InternalItemTypes}}
							<option value="{{this.value}}">{{this.label}}</option>
							{{/each}}
						</select>
					</div>
				</div>
				<!--EXPENSE-->
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label for="NonInventory_EXPENSE" class="cqo-label">Expense Account</label>
					</div>
					<div class="content-horiz-item-row-right">
						<input type="text" id="NonInventory_EXPENSE"
							   {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}
							   data-type-id="{{accounts.types.Expense}}"
							   decorator="UnifiedAutocomplete:{{accounts.url}},{{accounts.options}},{{accounts.events}},true"
							   value="{{this.AccountExpenseName}}"
							   autocomplete-item-value="{{this.AccountExpenseId}}"
							   placeholder="{{getDefaultPlaceholder(model.DefaultAccounts, accounts.types.Expense)}}"
							   class="cqo-input-text" />
					</div>
				</div>
				<!--SALES-->
				<div class="content-horiz-item-row clearfix">
					<div class="content-horiz-item-row-left">
						<label for="NonInventory_SALES" class="cqo-label">Sales Account</label>
					</div>
					<div class="content-horiz-item-row-right">
						<input type="text" id="NonInventory_SALES"
							   {{#if !toBoolean(this.IsEnabled)}} disabled {{/if}}
							   data-type-id="{{accounts.types.Income}}"
							   decorator="UnifiedAutocomplete:{{accounts.url}},{{accounts.options}},{{accounts.events}},true"
							   value="{{this.AccountSalesName}}"
							   autocomplete-item-value="{{this.AccountSalesId}}"
							   placeholder="{{getDefaultPlaceholder(model.DefaultAccounts, accounts.types.Expense)}}"
							   class="cqo-input-text" />
					</div>
				</div>

			</div>
			{{/with}}
		</div>


		<div class="content-vertic-item clearfix">
			<div class="content-vertic-item-data clearfix">
				<button class="button btnSelectClass" type="button" id="btQbSave" on-click="onSave">Save</button>
			</div>
		</div>


    </div>

</div>