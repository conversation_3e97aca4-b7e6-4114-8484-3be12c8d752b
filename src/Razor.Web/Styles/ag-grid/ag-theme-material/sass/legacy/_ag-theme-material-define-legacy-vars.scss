@import "../../../ag-theme-base/sass/legacy/ag-theme-base-rename-legacy-vars";

// In v23 we moved from using global variables to maps of parameters to configure Grid themes.
// This file is part of a backwards-compatibility layer designed to emulate the pre-v23 behaviour
// and so should not be modified.

// DO NOT EDIT (see note above)
$ag-mat-grey-0: #fff;
// DO NOT EDIT (see note above)
$ag-mat-grey-50: #fafafa;
// DO NOT EDIT (see note above)
$ag-mat-grey-100: #f5f5f5;
// DO NOT EDIT (see note above)
$ag-mat-grey-200: #eee;
// DO NOT EDIT (see note above)
$ag-mat-grey-300: #e2e2e2;
// DO NOT EDIT (see note above)
$ag-mat-indigo-500: #3f51b5;
// DO NOT EDIT (see note above)
$ag-mat-pink-A200: #ff4081;
// DO NOT EDIT (see note above)
$ag-mat-pink-50: #fce4ec;
// DO NOT EDIT (see note above)
$ag-mat-indigo-50: #e8eaf6;
// DO NOT EDIT (see note above)
$ag-foreground-opacity: 0.87 !default;
// DO NOT EDIT (see note above)
$ag-secondary-foreground-color-opacity: 0.54 !default;
// DO NOT EDIT (see note above)
$ag-disabled-foreground-color-opacity: 0.38 !default;
// DO NOT EDIT (see note above)
$ag-grid-size: 8px !default;
// DO NOT EDIT (see note above)
$ag-icon-size: 18px !default;
// DO NOT EDIT (see note above)
$ag-header-height: $ag-grid-size * 7 !default;
// DO NOT EDIT (see note above)
$ag-row-height: $ag-grid-size * 6 !default;
// DO NOT EDIT (see note above)
$ag-row-border-width: 1px !default;
// DO NOT EDIT (see note above)
$ag-toolpanel-indent-size: $ag-grid-size + $ag-icon-size !default;
// DO NOT EDIT (see note above)
$ag-row-group-indent-size: $ag-grid-size * 3 + $ag-icon-size !default;
// DO NOT EDIT (see note above)
$ag-cell-horizontal-padding: $ag-grid-size * 3 !default;
// DO NOT EDIT (see note above)
$ag-virtual-item-height: $ag-grid-size * 5;
// DO NOT EDIT (see note above)
$ag-header-icon-size: 14px !default;
// DO NOT EDIT (see note above)
$ag-font-family: "Roboto", sans-serif !default;
// DO NOT EDIT (see note above)
$ag-font-size: 13px !default;
// DO NOT EDIT (see note above)
$ag-font-weight: 400 !default;
// DO NOT EDIT (see note above)
$ag-secondary-font-family: "Roboto", sans-serif !default;
// DO NOT EDIT (see note above)
$ag-secondary-font-size: 12px !default;
// DO NOT EDIT (see note above)
$ag-secondary-font-weight: 700 !default;
// DO NOT EDIT (see note above)
$ag-card-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !default;
// DO NOT EDIT (see note above)
$ag-card-radius: 2px;
// DO NOT EDIT (see note above)
$ag-full-width-tabs: true;
// DO NOT EDIT (see note above)
$ag-background-color: $ag-mat-grey-0 !default;
// DO NOT EDIT (see note above)
$ag-foreground-color: rgba(#000, $ag-foreground-opacity) !default;
// DO NOT EDIT (see note above)
$ag-secondary-foreground-color: rgba(#000, $ag-secondary-foreground-color-opacity) !default;
// DO NOT EDIT (see note above)
$ag-disabled-foreground-color: rgba(#000, $ag-disabled-foreground-color-opacity) !default;
// DO NOT EDIT (see note above)
$ag-header-background-color: $ag-background-color !default;
// DO NOT EDIT (see note above)
$ag-header-cell-hover-background-color: darken($ag-header-background-color, 5%) !default;
// DO NOT EDIT (see note above)
$ag-header-cell-moving-background-color: $ag-header-cell-hover-background-color !default;
// DO NOT EDIT (see note above)
$ag-header-foreground-color: $ag-secondary-foreground-color !default;
// DO NOT EDIT (see note above)
$ag-input-disabled-background-color: transparent !default;
// DO NOT EDIT (see note above)
$ag-border-color: $ag-mat-grey-300 !default;
// DO NOT EDIT (see note above)
$ag-primary-color: $ag-mat-indigo-500 !default;
// DO NOT EDIT (see note above)
$ag-accent-color: $ag-mat-pink-A200 !default;
// DO NOT EDIT (see note above)
$ag-icon-color: #333 !default;
// DO NOT EDIT (see note above)
$ag-alt-icon-color: transparent !default;
// DO NOT EDIT (see note above)
$ag-editor-background-color: $ag-mat-grey-50 !default;
// DO NOT EDIT (see note above)
$ag-panel-background-color: $ag-mat-grey-200 !default;
// DO NOT EDIT (see note above)
$ag-tool-panel-background-color: $ag-mat-grey-50 !default;
// DO NOT EDIT (see note above)
$ag-chip-background-color: $ag-mat-grey-300 !default;
// DO NOT EDIT (see note above)
$ag-range-selection-background-color: transparentize(darken($ag-mat-indigo-50, 30%), 0.9) !default;
// DO NOT EDIT (see note above)
$ag-range-selection-chart-category-background-color: rgba(0, 255, 132, 0.1) !default;
// DO NOT EDIT (see note above)
$ag-range-selection-chart-background-color: rgba(0, 88, 255, 0.1) !default;
// DO NOT EDIT (see note above)
$ag-range-selection-highlight-color: $ag-mat-pink-50 !default;
// DO NOT EDIT (see note above)
$ag-hover-color: $ag-mat-grey-50 !default;
// DO NOT EDIT (see note above)
$ag-selected-color: $ag-mat-grey-200 !default;
// DO NOT EDIT (see note above)
$ag-cell-data-changed-color: $ag-mat-pink-50 !default;
// DO NOT EDIT (see note above)
$ag-value-change-delta-up-color: #43a047 !default;
// DO NOT EDIT (see note above)
$ag-value-change-delta-down-color: #e53935 !default;
// DO NOT EDIT (see note above)
$ag-value-change-value-highlight-background-color: #00acc1 !default;
// DO NOT EDIT (see note above)
@import "../ag-theme-material-font-vars";
// DO NOT EDIT (see note above)
$ag-icon-font-family: $ag-theme-material-icon-font-family !default;
// DO NOT EDIT (see note above)
$ag-icons-data: $ag-theme-material-icons-data !default;
// DO NOT EDIT (see note above)
$ag-icons-font-codes: $ag-theme-material-icons-font-codes !default;

@import "../../../ag-theme-base/sass/legacy/ag-theme-base-define-legacy-vars";