function RMA(isFromiFrame) {
    this.onCreate(isFromiFrame);
};

RMA.prototype = {
    _shippingLoaded: false,

    _data: {
        dialogHtml: null,
        rmaId: -1,
        salesOrderId: -1,
        stepValidateErrors: ["", "", "", ""],
        currState: [],
        generatedOrderId: null,
        isFromiFrame: false,
        rmaItems: new Map() //  when grid data changed (search, next page) the selected for rma items will lost, therefore they stored here
    },

    _rmaTypeGroup: null,

    _urls: {
        pickListPage: Common.baseUrl + '/Admin/RMA.aspx',
        getItemsForRMA: Common.baseUrl + '/Services/RMAService.asmx/GetItemsForRMA',
        getCustomerEmail: Common.baseUrl + '/Services/RMAService.asmx/GetCustomerEmail',
        getReturnReasons: Common.baseUrl + '/Services/RMAService.asmx/ListRMAReturnReasons',
        saveNewReturnReason: Common.baseUrl + '/Services/RMAService.asmx/SaveNewReturnReason',
        saveRMA: Common.baseUrl + '/Services/RMAService.asmx/SaveRMA',
        getRMA: Common.baseUrl + '/Services/RMAService.asmx/GetRMA',
        listRmaTypes: Common.baseUrl + '/Services/RMAService.asmx/GetRmaTypes',
        sendEmail: Common.baseUrl + '/Services/RMAService.asmx/SendEmail'
    },

    _sel: {
        btFinish: '.buttonFinish',
        RMAWizard: "#RMAWizard",
        tbSearch: "#tb_RMAItemsSearch",
        ddReasons: "#dd_RMAItemsReason",
        btSetReasons: "#bt_SetRMAItemsReason",
        grid: "#jqgRMA",
        pager: "#jqpRMA",
        edEmail: "#ed_Email",
        ddRMAType: "#ddRMAType",
        ddProvideReturnLabel: "#ddProvideReturnLabel",
        divProvideReturnLabel: "#divProvideReturnLabel",
        ckSendEmail: '#sendEmail',
        cbHeader: "#cb_jqgRMA",
        edInternalComment: "#ed_InternalComment",
        edRmaComment: "#ed_RmaComment"
    },

    _controls: {
        grid: null,
        dialog: null,
        search: null,
        wizard: null,
        RMAShipping: null,
        returnReason: null
    },

    Events:
    {
        OnSubmit: []
    },

    onCreate: function (isFromiFrame) {
        var it = this;

        it._data.isFromiFrame = isFromiFrame;

        if (!isFromiFrame) {
            var pageRequest = Url.GetPage(it._urls.pickListPage);
            if (pageRequest && pageRequest.result == true && pageRequest.html) {
                it._data.dialogHtml = pageRequest.html;
            }
        }
        it.needExtraProcessStep1 = true;
    },

    Open: function (rmaId, salesOrderId, readOnly, shippingId, context) {
        let it = this;

        it._data.rmaItems = new Map();

        it._data.readOnly = readOnly === true;
        it._data.rmaId = rmaId;
        it._data.salesOrderId = Number.parse(salesOrderId, null);
        it._data.shippingId = Number.parse(shippingId, null);

        let onShow = function () {
            if (!it._data.isFromiFrame) {
                it._controls.dialog = this;
            }
            it.createControls(context);
            it.bindEvents();
            it.toggleReadOnly();
            $(it._sel.tbSearch).GridSearch({ grid: it._controls.grid });
            $(it._controls.grid).reloadGrid(true);
            it.initShippingPage();
        };

        if (!it._data.isFromiFrame && it._data.dialogHtml != null) {
            ShowMModalDialog(it._data.dialogHtml,
                {
                    title: "RMA",
                    width: 1010,
                    height: 600,
                    position: "center",
                    modal: true,
                    resizable: false
                },
                onShow);
        }
        else {
            if (it._controls.wizard) {
                it.needExtraProcessStep1 = true;
                it._controls.wizard.smartWizard('goToStep', 1);
            }
            onShow();
        }
    },

    toggleReadOnly: function () {
        var it = this;
        $(it._sel.ddRMAType)
            .add(it._sel.ddProvideReturnLabel)
            .add(it._sel.edEmail)
            .add(it._sel.ckSendEmail)
            .toggleEnabled(!it._data.readOnly);
    },

    initShippingPage: function () {
        var it = this;
        it._controls.RMAShipping = new RMAShipping(it._data.readOnly);
        it.loadRmaShipping([], false);
    },

    createControls: function (context) {
        var it = this;
        it._controls.wizard = $(it._sel.RMAWizard).smartWizard({
            keyNavigation: false,
            enableAllSteps: it._data.readOnly,
            onFinish: function () {
                // validate
                if (it._data.readOnly) {
                    $(it._controls.dialog).dialog("close");
                } else {
                    if (it.validateSteps()) {
                        var data = it.collectData();
                        data.NeedGenerateOrder = true;
                        if (Number.isNumber(it._data.generatedOrderId)) {
                            ShowConfirm('Do you want to Regenerate Sales Order?', null, function () {
                                data.NeedGenerateOrder = true;
                                it.saveData(data);
                            }, function () {
                                data.NeedGenerateOrder = false;
                                it.saveData(data);
                            });
                        } else {
                            it.saveData(data);
                        }
                    }
                }
            },
            onLeaveStep: function (obj, context) {
                if (context.fromStep === 1) {
                    it.setReturnReason();
                }

                var result = context.fromStep > context.toStep || it.validateSteps(context.fromStep);
                if (result) {

                    if (context.fromStep == 1 && it.needExtraProcessStep1) {
                        result = false;
                        $(':focus').blur();
                        _.defer(function () {
                            it.needExtraProcessStep1 = false;
                            it._controls.wizard.smartWizard('goToStep', context.toStep);
                        });
                    }

                    var needLabel = it.isNeedReturnLabel();
                    if (context.fromStep == 2) {
                        it._controls.wizard.smartWizard('enableStep', 3);
                    }

                    if (context.toStep == 3) {
                        // labels needed - activate step 3
                        if (needLabel) {
                            const items = Array.from(it._data.rmaItems.keys());
                            const itemIds = _.map(items, item => Number(item));

                            it.loadRmaShipping(itemIds, true);
                            // skip to 4
                        } else {
                            it._controls.wizard.smartWizard('goToStep', context.fromStep > context.toStep ? 2 : 4);
                            result = false;

                        }
                    }
                    if (context.fromStep == 3 && context.toStep > context.fromStep && it._controls.RMAShipping._controls.shippingProviders.find('option').length == 0) {
                        ShowAlert("Shipping Providers is not set.");
                        result = false;
                    }
                }
                it.needExtraProcessStep1 = true;
                return result;
            }
        });
        //$(it._sel.RMAWizard).smartWizard('fixHeight');

        it.InitRMAItemsGrid(context);

        // Create radio group
        var response = Url.PostSync(it._urls.listRmaTypes);
        ProcessAjaxItemResponse(response, function (data) {
            if (!Array.isNullOrEmpty(data)) {
                $(data).each(function (i, item) {
                    $(it._sel.ddRMAType).append(
                        '<div class="rma-list-radio">' +
                        '<input type="radio" value="' + item.value + '" name="rmaType" id="cb_View_' + item.value + '"/>' +
                        '<label for="cb_View_' + item.value + '" class="">' + item.label + '</label>' +
                        '</idv>');
                });
            }
        });
        it._rmaTypeGroup = $(it._sel.ddRMAType).find('input[name="rmaType"]');

        it._controls.returnReason = new UnifiedAutocomplete($(it._sel.ddReasons), it._urls.getReturnReasons,
            {
                discardUnknown: false,
                showAllVariants: true
            },
            {
                onChange: $.proxy(it.onAutocompleteChange, it)
            }
        );

        $(it._sel.btSetReasons).off('click').on('click', function (event) {
            event.preventDefault();
            var selRows = it._controls.grid.getSelGridRows(),
                reason = it._controls.returnReason.GetData();
            if (reason) {
                $.each(selRows, function (pos, row) {
                    if (!it._data.readOnly) {
                        var el = $('#' + extractHtmlAttribute(row.ReturnReasonUA, "id"));
                        _.defer(function () {
                            UnifiedAutocomplete.Get(el).SetData({ label: reason.label, value: reason.value });
                        });
                    }
                });
            }
        });

        if (it._data.rmaId == -1) { // new rma
            // get customer Email
            Url.PostAsync(it._urls.getCustomerEmail, { salesOrderId: it._data.salesOrderId }, function (data) {
                ProcessAjaxItemResponse(data, function (item) {
                    if (item) {
                        $(it._sel.edEmail).val(item);
                    }
                });
            });
            $("input[name='rmaType'][value='1']").prop('checked', true);
        } else { //edit rma
            Url.PostAsync(it._urls.getRMA, { rmaId: it._data.rmaId }, function (data) {
                ProcessAjaxItemResponse(data, function (item) {
                    if (item) {
                        $("input[name='rmaType'][value=" + item.RMATypeId + "]").prop('checked', true);
                        if (item.RMATypeId == 3) {
                            $(it._sel.divProvideReturnLabel).hide();
                        }
                        if (item.ProvideReturnLabel) {
                            $(it._sel.ddProvideReturnLabel).val(1);
                        } else {
                            $(it._sel.ddProvideReturnLabel).val(0);
                        }
                        $(it._sel.edEmail).val(item.Email);
                        $(it._sel.edRmaComment).val(item.Description);
                        $(it._sel.edInternalComment).val(item.InternalComment);

                        it._data.generatedOrderId = item.GeneratedOrderId;
                    }
                });
            });
        }
    },

    loadRmaShipping: function (salesOrderItemIds, isUpdate) {
        var it = this;
        if (isUpdate) {
            it._controls.RMAShipping.OnReturnToEdit(salesOrderItemIds);
            return;
        }

        if (isNumber(it._data.rmaId) && Number(it._data.rmaId) > 0) {
            it._controls.RMAShipping.LoadFromServer(it._data.rmaId, it._data.salesOrderId, salesOrderItemIds, it._data.shippingId);
            return;
        }

        it._controls.RMAShipping.LoadInitaly(it._data.salesOrderId, salesOrderItemIds, it._data.shippingId);
    },

    bindEvents: function () {
        var it = this;

        it._rmaTypeGroup.off("change").on("change", function (event) {
            var rmaType = it._rmaTypeGroup.valueOfChecked();
            if (rmaType == 3) {
                $(it._sel.divProvideReturnLabel).hide();
            } else {
                $(it._sel.divProvideReturnLabel).show();
            }
        });
    },

    InitRMAItemsGrid: function (context) {
        var it = this;

        var extNumber = FormattersFactory.GetSimpleFormatter('Number').GetColModelExtension(true);
        var extBoolean = SimpleFormatters.Boolean;
        var extCurrency = FormattersFactory.GetFormatter('PayChargeValue', { formatoptions: { context: context ? context : UserSettings } }).GetColModelExtension(false);
        // init the grid
        const returnReasonCellName = 'ReturnReasonUA';
        it._controls.grid = $(it._sel.grid)
            .jqGrid({
                pager: it._sel.pager,
                url: it._urls.getItemsForRMA,
                type: "POST",
                datatype: "local",

                ajaxGridOptions: { contentType: "application/json; charset=utf-8" },
                jsonReader: {
                    root: "d.rows",
                    id: "SalesOrderId",
                    page: "d.page",
                    total: "d.total",
                    records: "d.records",
                    repeatitems: false
                },
                prmNames: { nd: null, page: "page", rows: "rows" },
                serializeGridData: function (postData) {
                    postData.Data = {
                        RMAId: it._data.rmaId,
                        SalesOrderId: it._data.salesOrderId
                    };
                    return "request=" + encodeURIComponent(JSON.stringify(postData));
                },
                shrinkToFit: false,
                width: 980,
                height: "auto",
                colNames: ["SalesOrderItemId", "Selected", "Model", "UID", "Serial", "Manufacturer", "Description", "Return Reason", "ReturnReasonId", "Return Reason", "Price"],
                colModel: [
                    $.extend({ name: "SalesOrderItemId", hidden: true, editable: false }, extNumber),
                    $.extend({ name: "Selected", hidden: true }, extBoolean),
                    { name: "Model", width: 120, editable: false, search: true },
                    { name: "UniqueId", width: 120, editable: false, search: true },
                    { name: "Serial", width: 120, editable: false, search: true },
                    { name: "Manufacturer", width: 120, editable: false, search: true },
                    { name: "Description", width: 225, editable: false, search: true },
                    {
                        name: returnReasonCellName, width: 135, editable: false, search: false, sortable: false,
                        formatter: it._data.readOnly
                            ? jqgFormatterReason
                            : jqgFormatterAutoComplete,
                        formatoptions: {
                            change: $.proxy(it.onAutocompleteChange, it),
                            url: it._urls.getReturnReasons,
                            discardUnknown: false,
                            restoreIfEmpty: true,
                            handlers: {
                                onChange: $.proxy(it.onAutocompleteChange, it),
                                afterSelect: function(text, item, context) {
                                    const rowId = $(context._sel.input).parents('tr').prop('id');
                                    const isSelected = _.contains(it._controls.grid.getSelGridRowIds(), rowId);

                                    if (item && !isSelected || !item && isSelected) {
                                        it._controls.grid.setSelection(rowId);
                                    }
                                }
                            }
                        }
                    },
                    $.extend({ name: "ReturnReasonId", hidden: true }, extNumber),
                    { name: "ReturnReasonCD", hidden: true },
                    $.extend({ name: "Price", width: 70, editable: false, search: true }, extCurrency)
                ],
                sortname: "Model",
                sortorder: "desc",
                multiselect: true,
                multiboxonly: it._data.readOnly,
                multiselectWidth: 25,
                rowNum: CommonGridParams.DefaultRows,
                rowList: [10, 25, 50, 100, 250, 500, 1000, 2000], //CommonGridParams.RowList, RSW-9375
                gridview: true,

                viewrecords: true,
                altRows: true,
                altclass: "grey_td",
                _selectedRowId: -1,

                beforeSelectRow: function (rowId, event) {
                    const $clicked = $(event.srcElement);
                    const $td = $(event.srcElement).is('td')
                        ? $clicked
                        : $clicked.parents('td').first();
                    const isReturnReason = $td.length > 0 &&
                        ($td.attr('aria-describedby') ?? "").endsWith(returnReasonCellName);

                    if (it._data.readOnly || isReturnReason) {
                        return false;
                    }
                    return $(it._controls.grid).handleJqGridMultiSelect(rowId, event);
                },

                onSelectRow: function (rowId, isSelected, event) {
                    it.onSelectRow(rowId, isSelected);
                    it._controls.grid.updateSelectAll();
                },

                onSelectAll: function (rowIds, isSelected) {
                    rowIds.forEach(rowId => it.onSelectRow(rowId, isSelected));
                },

                beforeRequest: function () {
                    if (it._controls.grid) {
                        it.setReturnReason();
                        return it.validateReturnReasonRequired();
                    }
                    return true;
                }
            });

        it._controls.grid.bind('jqGridAfterGridComplete', function () {
            if (it._controls.grid) {
                if (it._data.readOnly) {
                    $(it._sel.grid)
                        .find('[type="checkbox"]')
                        .add(it._sel.cbHeader)
                        .prop("disabled", true);
                    it._controls.grid.showCol("ReturnReasonCD");
                    it._controls.grid.hideCol("ReturnReasonUA");
                }

                if (it._controls.grid.getGridParam("datatype") == "local") {
                    return;
                }

                $(it._controls.grid).resetSelection();
                _.each(it._controls.grid.getDataIDs(), function (rowId) {
                    var row = it._controls.grid.getRowData(rowId);
                    // restore selection of previously checked item
                    const rmaItem = it._data.rmaItems.get(row.SalesOrderItemId);
                    if (rmaItem) {
                        row.Selected = true;
                        rmaItem.ReturnReasonUA = row.ReturnReasonUA;
                        row.ReturnReasonCD = rmaItem.ReturnReasonCD;
                        row.ReturnReasonId = rmaItem.ReturnReasonId;
                    }

                    if (!row.Selected) {
                        $(it._controls.grid).resetSelection(rowId);
                        return;
                    }

                    $(it._controls.grid).setSelection(rowId);
                    if (!it._data.readOnly) {
                        var el = $('#' + extractHtmlAttribute(row.ReturnReasonUA, "id"));
                        _.defer(function () {
                            UnifiedAutocomplete.Get(el).SetData({ label: row.ReturnReasonCD, value: row.ReturnReasonId });
                        });
                    }
                });
                it._controls.grid.updateSelectAll();
            }
        });

        // hide buttons
        $.extend($.jgrid.nav, {
            add: false,
            edit: false,
            del: false,
            refresh: true,
            search: false
        });
        it._controls.grid.jqGrid('navGrid', it._sel.pager);
    },

    onSelectRow: function (rowId, isSelected) {
        var it = this;
        const row = it._controls.grid.getRowData(rowId);
        const el = $('#' + extractHtmlAttribute(row.ReturnReasonUA, "id"));
        if (!isSelected) {
            $(el).removeClass('autocompleteError');
            UnifiedAutocomplete.Get(el).Clear();
            it._data.rmaItems.delete(row.SalesOrderItemId);
        } else if (!it._data.rmaItems.has(row.SalesOrderItemId)) {
            const returnReasonId = UnifiedAutocomplete.Get(el)?.GetData()?.value;
            it._data.rmaItems.set(row.SalesOrderItemId,
                {
                    SalesOrderItemId: row.SalesOrderItemId,
                    ReturnReasonId: returnReasonId,
                    ReturnReasonUA: row.ReturnReasonUA,
                    ReturnReasonCD: row.ReturnReasonCD
                });
        }
        it._controls.grid.updateSelectAll();
    },

    onAutocompleteChange: function (e) {
        var it = this;
        var el = e.target;
        var textData = $(el).val();
        $(el).removeClass('autocompleteError');
        var uaData = UnifiedAutocomplete.Get(el).GetData();
        if ((!uaData || uaData.label != textData) && $.trim(textData).length > 0) {
            ShowConfirm("Do you want to add a new return reson \"" + textData + "\"?", "Confirm", function () {
                Url.PostAsync(it._urls.saveNewReturnReason, { returnReason: textData }, function (data) {
                    ProcessAjaxItemResponse(data, function (item) {
                        if (item) {
                            UnifiedAutocomplete.Get(el).SetData({ label: textData, value: item });
                        }
                    });
                });
            }, function () {
                if (typeof (uaData) == "undefined") {
                    $(el).val("");
                } else {
                    UnifiedAutocomplete.Get(el).SetData(uaData);
                }
            });
        }
    },

    // saves return reason for selected items
    setReturnReason: function () {
        const it = this;
        it._data.rmaItems.forEach(item => {
            const el = $('#' + extractHtmlAttribute(item.ReturnReasonUA, "id"));
            if (el.length) {
                const option = UnifiedAutocomplete.Get(el).GetData();
                item.ReturnReasonId = option ? option.value : null;
                item.ReturnReasonCD = option ? option.label : '';
            }
        });
    },

    collectData: function () {
        var it = this;
        var data = {
            RMAId: it._data.rmaId,
            SalesOrderId: it._data.salesOrderId,
            RMATypeId: it._rmaTypeGroup.valueOfChecked(),
            ProvideReturnLabel: Boolean(Number($(it._sel.ddProvideReturnLabel).val())),
            Email: $(it._sel.edEmail).val(),
            SendEmail: $(it._sel.ckSendEmail).is(':checked'),
            Items: _.map(Array.from(it._data.rmaItems.values()), item => ({ SalesOrderItemId: item.SalesOrderItemId, ReturnReasonId: item.ReturnReasonId })),
            Shipping: it._controls.RMAShipping.GetData(),
            InternalComment: $(it._sel.edInternalComment).val(),
            Description: $(it._sel.edRmaComment).val()
        };
        
        return data;
    },

    saveData: function (model) {
        var it = this;

        $(it._sel.btFinish).disableButton(true);
        var response = Url.PostSync(it._urls.saveRMA, { data: model });
        ProcessAjaxItemResponse(response, function (item) {
            it._data.rmaId = item.RmaId;
            function onDone() {
                if ($(it._controls.dialog).is(':visible')) {
                    $(it._controls.dialog).dialog('close');
                }

                let generateResult = model.ProvideReturnLabel && model.RMATypeId != 3
                    ? " Shipping label has been generated."
                    : "";
                if (!String.isNullOrEmpty(item.Error)) {
                    NoticeAlert.showError('Error:', item.Error);
                    generateResult = "";
                }

                // Sales Order passage
                var msgSo = '';
                if (!String.isNullOrEmpty(item.SalesOrderId)) {
                    var linkSo = LinkFormatters.SalesOrder.invokeStraight(item.SalesOrderId, item.SalesOrderName, 1);
                    msgSo = String.format('Sales Order #{0} has been {1}.',
                        linkSo,
                        Number.isNumber(it._data.generatedOrderId)
                            ? "re-generated"
                            : "generated");
                }

                // Rma passage
                var linkRma = LinkFormatters.Rma.invokeStraight(item.RmaId, model.RMATypeId == 3 ? 6 : 1);
                var msgRma = String.format('RMA #{0} has been {1}. ',
                    linkRma,
                    isNumber(model.RMAId) && model.RMAId > 0
                        ? 'saved'
                        : 'created'
                );

                // show message
                NoticeAlert.showLightInfo('Information', msgRma + msgSo + generateResult);
                invokeEvent(it.Events.OnSubmit);
            }

            // try send email now
            if (model.SendEmail) {
                var responseEmail = Url.PostSync(it._urls.sendEmail, { rmaId: it._data.rmaId });
                ProcessAjaxItemResponse(responseEmail, onDone,
                    function (message) {
                        NoticeAlert.showError('Error sending the e-mail', message);
                    });
            } else {
                onDone();
            }
        });
        $(it._sel.btFinish).enableButton();
    },

    validateSteps: function (stepNum) {
        var it = this;
        if (it._data.readOnly) {
            return true;
        }
        var currStep = stepNum;
        var isStepValid = true;

        if (typeof (stepNum) == "undefined") {
            isStepValid = it.validateStep1();
            currStep = 1;
            if (isStepValid) {
                isStepValid = it.validateStep2();
                currStep = 2;
                if (isStepValid) {
                    if (it.isNeedReturnLabel()) {
                        isStepValid = it.validateStep3();
                        currStep = 3;
                    }
                }
            }
        } else {
            switch (stepNum) {
                case 1:
                    {
                        isStepValid = it.validateStep1();
                        break;
                    }
                case 2:
                    {
                        isStepValid = it.validateStep2();
                        break;
                    }
                case 3:
                    {
                        isStepValid = it.validateStep3();
                        break;
                    }
            }
        }

        if (!isStepValid) {
            it._controls.wizard.smartWizard('setError', { stepnum: currStep, iserror: true });
            it._controls.wizard.smartWizard('showMessage', it._data.stepValidateErrors[currStep - 1]);
        } else {
            it._controls.wizard.smartWizard('setError', { stepnum: currStep, iserror: false });
            it._controls.wizard.smartWizard('hideMessage');
        }
        return isStepValid;
    },

    validateStep1: function () {
        var it = this;
        var isValid = true;
        if (it._data.rmaItems.size === 0) {
            it._data.stepValidateErrors[0] = "At least one item should be selected";
            isValid = false;
        } else {
            it.validateReturnReasonRequired();

            if (Array.from(it._data.rmaItems.values()).some(item => !item.ReturnReasonId)) {
                it._data.stepValidateErrors[0] = "Return reason should be chosen";
                isValid = false;
            }
        }
        
        return isValid;
    },

    validateReturnReasonRequired: function () {
        var it = this;
        var isValid = true;
        it._data.rmaItems.forEach(item => {
            var el = $('#' + extractHtmlAttribute(item.ReturnReasonUA, "id"));
            if (el.length) {
                var data = $(el).val();
                if ($.trim(data).length == 0) {
                    $(el).addClass('autocompleteError');
                    it._data.stepValidateErrors[0] = "Return reason should be chosen";
                    isValid = false;
                }
            }
        });

        return isValid;
    },

    validateStep2: function () {
        var it = this;
        var str = $(it._sel.edEmail).val();
        var emails = str.split(', ');
        for (var i in emails) {
            if (emails.hasOwnProperty(i)) {
                $(it._sel.edEmail).val(emails[i]);
                var error = $(it._sel.edEmail).TextValidateEmail(true, "Email");
                if (error) {
                    it._data.stepValidateErrors[1] = error;
                    $(it._sel.edEmail).val(str);
                    return false;
                }
            }
        }
        $(it._sel.edEmail).val(str);
        return true;
    },

    validateStep3: function () {
        var it = this;
        var result = it._controls.RMAShipping.TextValidate();
        if (result) {
            it._data.stepValidateErrors[2] = result;
            return false;
        } else {
            return true;
        }
    },

    isNeedReturnLabel: function () {
        var it = this;
        var rmaType = it._rmaTypeGroup.valueOfChecked();
        if (rmaType == 3)
            return false;
        return $(it._sel.ddProvideReturnLabel).val() == 1;
    }

};

function jqgFormatterReason(cellvalue, options, rowObject, action) {
    return rowObject.ReturnReasonCD ? rowObject.ReturnReasonCD : "";
};
