<div class="title-sales-order">
    <p class="title-sales-paragraph">
        <span class="title-paragraph-left">SALES ORDER: </span>
        <span class="title-paragraph-right sales-order-title"></span>
    </p>
</div>

<style>
	/*.title-detail-sales { margin-top: 110px; }*/
    .detail-sales-info {
        margin-bottom: 20px;
        padding: 0;
    }
    .tabs-order.flw {
        margin-top: 113px;
    }
	.opt-customer { width: 330px; }
		.opt-customer input[type="text"] { width: 190px; }
		.opt-customer > a {
			float: left;
			margin-left: 10px;
			margin-top: 2px;
			width: 32px;
		}
	.opt-template-terms { width: 290px; }
		.opt-template-terms select {
			vertical-align: sub;
			width: 165px;
		}

	.ac-headers select {
		outline: none;
		padding: 5px 0 5px 3px;
		margin: 0 3px 0 0px;
		border: 1px solid #DDDDDD;
		resize: none;
		box-shadow: 0 0 2px 0px #CCCCCC;
		vertical-align: top;
	}

    .select-detail-block {
        margin-top: 28px;
    }

        .select-detail-block.sales-rep {
            width: 20%;
            margin-right: 0px;
        }

    .sales-rep select{
        width: 252px;
    }
    .wrap-tabs-sales-order {
        margin-top: -4px;
    }

    .title-detail-sales {
        padding-bottom: 0;
    }

    .tabs-table-item {
        margin-right: 0;
        margin-bottom: -1px; 
        margin-top: 8px;
        cursor: pointer;
        color: #666 !important;
        font: 700 12px/15px Tahoma, Geneva, Kalimati, sans-serif;
        padding: 8px 25px;
        border: 1px solid #CECECE;
        border-bottom: none;
        text-decoration: none;
        text-shadow: 0 1px 0 rgba(255,255,255, .75);
        box-shadow: 0 -1px 3px 0px rgba(0,0,0, .2);
        background: #ebebeb !important;
        display: inline-block;
        box-sizing: border-box;
    }

        .tabs-table-item.ui-tabs-active.ui-state-active {
            background: #ffffff !important;
            border: 1px solid #C1C1C1 !important;
            border-bottom: 0 !important;
            outline: none;
            cursor: pointer !important;
            color: #333 !important;
            font-weight: 700;
            height: auto !important;
            margin-top: 4px;
            padding: 10px 20px !important;
        }

    ul.tabs-table-title {
        margin: 0;
        margin-bottom: -2px;
    }

    .tabs-table-content {
        padding-top: 10px;
    }

	    .sales-list-right .ac-input {
		    box-sizing: border-box;
		    max-width: 100%;
	    }

    .tabs-sales-list {
        padding-bottom: 8px;
    }

        .tabs-sales-list li {
            float: left;
            margin-right: 8px;
        }

    .tabs-sales-span-left,
    .tabs-sales-span-right {
        width: auto;
        padding-right: 8px;
    }

    .tabs-sales-item {
        min-height: 50px;
    }

        .tabs-sales-list.__block-items li {
            float: none;
            margin-bottom: 0;
        }

        .tabs-sales-list_item .open-details {
            font-size: 16px;
            cursor: pointer;
            margin-left: 5px;
            vertical-align: -1px;
            display: inline-block;
        }

        .tabs-sales-list_item .package-status-span {
            cursor: pointer;
        }

        .tabs-sales-list_item .shipment-details {
            display: none;
        }

        .tabs-sales-list_item.__open-details .shipment-details {
            display: list-item;
            list-style-type: none;
            padding-left: 0;
        }

	.tabs-sales-text {}
		.tabs-sales-text p {
			font-size: 12px;
			margin: 0;
		}
			/*.tabs-sales-text p.text-pre-wrap { white-space: pre-wrap;}*/
    .tabs-sales_item-title {
        margin: 0;
        font-size: 13px;
        font-weight: normal;
    }
    
    .span-instead-of-input {
        margin-top: 3px;
        display: block;
    }
	.tabs-sales-span-right.gray { color: #333333; }

    .button.btnSelectClass.proforma-new {
        margin: auto;
        float: right;
    }
    #span_ProformaInvoiceAutoName {
        width: auto;
    }

    .sales-info-list.ul-wide .sales-list-left.plus {
        width: 148px;
    }
    .ammount-due span {
        padding-top: 0;
    }
    .total-block-list li {
        height: 30px;
        margin: 0;
    }

    .status-pending {
        margin-bottom: 15px;
    }

    table.table-tax-rates {
        width: 100%;
    }

    table.table-tax-rates td {
        padding: 3px;
    }

    table.table-tax-rates tr:nth-child(even) {
        background: #f2f3fe;
    }

    table.table-tax-rates thead {
        background-color: #dfdfdf;
    }

    table.table-tax-rates th {
        font-weight: normal;
    }

    table.table-tax-rates td input {
        width: 100%;
        box-sizing: border-box;
    }

    table.table-tax-rates td span {
        display: block;
        padding: 2px 0 2px 1px;
    }

    table.table-tax-rates tr.tax-group td span {
        font-weight: bold;
    }

    table.table-tax-rates tr.tax-indent-row td:first-child {
        padding-left: 24px;
    }
</style>

<div class="page-content">
    <!-- HEADER BUTTONS -->
    <menusalesorder on-deleted="redirectToNew" on-change="refreshReloadable" fixed="{{model.Fixed}}" model="{{model.Reloadable}}" />

    <!-- SALES ORDER EDITOR -->
    <div class="under-buttons-block flw">
        <a href="#" class="wrap-tabs-sales-order-toggle-panel__toggle-panel-open toggleButtonShow toggleHidden"></a>
        <div class="detail-sales-order flw centerBlockDiv content-wrapper">

            <!--<div id="rSalesOrder"></div>-->
            <!-- HEADER AUTOCOMPLETES -->
			<div class="title-detail-sales ac-headers flw">
				<!-- Customer -->
				<div class="select-detail-block opt-customer">
					<!--RSW-8700: remove on-change because saving is provided by afterSelect of autocomplete-->
					<input type="hidden" for="ac_customer" value="{{model.Fixed.CustomerId}}" lazy="false" />
                    <h4 class="select-detail-h4">Customer</h4>
					<input type="text"
						   id="ac_customer"
						   class="left"
						   value="{{model.Fixed.CustomerName}}"
						   decorator="autocomplete:{{view.autocomplete.customers.dataSource}},{{view.autocomplete.customers.options}}, {{view.autocomplete.customers.events}}"
						   {{#if view.isInvoiceClosed}} disabled="disabled" {{/if}} />
					<!--{{#if model.Fixed.CustomerId > 0 }}-->
				    <!--<a href="{{view.format.castomerEditUrl(model.Fixed.CustomerId)}}" target="_blank" id="a_EditCustomer">Edit</a>-->
                    <a href="#" id="a_EditCustomer" on-click="editCustomer">Edit</a>
					<!--{{/if}}-->

                </div>

				<!--PO Number-->
                <div class="select-detail-block">
					<h4 class="select-detail-h4"><label for="dd_PoNo">PO No.</label></h4>
					<input id="dd_PoNo" type="text" class="left" value="{{model.Fixed.PoNumber}}" on-change="saveSalesOrder" 
						   {{#if !view.isInputEditable(view.isInvoiceClosed, model.Fixed)}} disabled="disabled" {{/if}} />
                </div>


                <div class="select-detail-block">
                    <h4 class="select-detail-h4"><label for="dd_Terms">Terms</label></h4>
                    {{#if view.havePermissionToSetTerms()}}
					<select id="dd_Terms" value="{{model.Reloadable.TermsId}}" on-change="saveSalesOrder" {{#if !view.isInputEditable(view.isInvoiceClosed, model.Fixed)}} disabled="disabled" {{/if}}>
                        {{#view.dropDowns.Terms}}
                        <option value="{{value}}">{{label}}</option>
                        {{/view.dropDowns.Terms}}
                    </select>
                    {{else}}
                    <label id="dd_Terms" class="span-instead-of-input">{{view.getTermsText(model.Reloadable.TermsId, view.dropDowns.Terms)}}</label>
                    <!--<input type="hidden" value="{{model.Reloadable.TermsId}}" />-->
                    {{/if}}
                </div>

                <div class="select-detail-block" style="width: 290px;">
                    <h4 class="select-detail-h4"><label for="dd_SoInvoiceTermsTemplate">Terms Template</label></h4>
                    <select id="dd_SoInvoiceTermsTemplate" value="{{model.Reloadable.TermsTemplateId}}" style="vertical-align: sub; width: 165px;" on-change="saveSalesOrder" {{#if !view.isInputEditable(view.isInvoiceClosed, model.Fixed)}} disabled="disabled" {{/if}}>
						{{#view.dropDowns.TermsTemplates}}
						<option value="{{value}}">{{label}}</option>
						{{/view.dropDowns.TermsTemplates}}
					</select>
                </div>

                <div class="select-detail-block sales-rep">
                    <h4 class="select-detail-h4"><label for="dd_RepUsers">Rep</label></h4>
                    <select id="dd_RepUsers" class="left" multiple="multiple" decorator="select2:{{view.autocomplete.repUsers}}" value="{{model.Fixed.RepUserIds}}"
                            {{#if !view.isInputEditable(view.isInvoiceClosed, model.Fixed)}} disabled="disabled" {{/if}}>
                        {{#model.Fixed.RepUsers}}
                        <option value="{{value}}">{{label}}</option>
                        {{/model.Fixed.RepUsers}}
                    </select>
                </div>

            </div>

            <!-- SALES ORDER DETAILS -->
            <div class="detail-sales-info flw" >

            <div class="wrap-sales-list">

                <h3 id="hd_Title" class="sales-info-title">
                    <span class="title-paragraph-left">SALES ORDER: </span>
                    <span class="title-paragraph-right sales-order-title">{{model.Fixed.AutoName}} {{model.Fixed.CustomerName}} {{#if model.Fixed.SalesOrderId > 0 }} ({{model.Reloadable.StatusName}}) {{/if}}</span>
                </h3>

                <ul class="sales-info-list">
                    <li class="">
                        <span class="sales-list-left">Customer:</span>
                        <span id="span_Customer" class="sales-list-right">{{model.Fixed.CustomerName}}</span>
                    </li>
                    <li class="">
                        <span class="sales-list-left">SO #:</span>
                        <span id="span_SoNo" class="sales-list-right">{{model.Fixed.AutoName}}</span>
                    </li>
                    <li class="">
                        <span class="sales-list-left">Date:</span>
                        <span class="sales-list-right">
                            <input type="text"
                                   readonly="readonly"
                                   id="tb_Date1"
                                   style="width: 100%;"
                                   value="{{model.Fixed.SalesOrderDate}}"
                                   on-change="saveSalesOrder"
                                   decorator="datepicker"
                                   {{#if !view.isInputEditable(view.isInvoiceClosed, model.Fixed)}} disabled="disabled" {{/if}} />
                        </span>
                    </li>
                    <li class="">
                        <span class="sales-list-left">Need By:</span>
                        <span class="sales-list-right">
                            <input type="text"
                                   readonly="readonly"
                                   style="width: 100%;"
                                   on-change="saveSalesOrder"
                                   value="{{model.Fixed.NeedByDate}}"
                                   decorator="datepicker"
                                   {{#if !view.isInputEditable(view.isInvoiceClosed, model.Fixed)}} disabled="disabled" {{/if}} />
                        </span>
                    </li>
                    <li class="">
                    <span class="sales-list-left">Shipping Date :</span>
                    <span class="sales-list-right">
                        <input type="text"
                               readonly="readonly"
                               style="width: 100%;"
                               on-change="saveSalesOrder"
                               value="{{model.Fixed.ShippingDate}}"
                               decorator="datepicker"
                               disabled="disabled" />
                    </span>
                    </li>
                    <li class="">
                        <span class="sales-list-left">Warranty:</span>
                        <span class="sales-list-right">
                            <select value="{{model.Fixed.WarrantyId}}" on-change="saveSalesOrder"
                                    {{#if !view.isInputEditable(view.isInvoiceClosed, model.Fixed)}} disabled="disabled" {{/if}}>
                                {{#view.dropDowns.Warranties}}
                                <option data-days="{{DurationDays}}" value="{{value}}">{{label}}</option>
                                {{/view.dropDowns.Warranties}}
                            </select>
                        </span>
                    </li>
                    <li class="">
                        <span class="sales-list-left">Expiration:</span>
                        <span class="sales-list-right">
                            <label>{{view.format.expiration(model.Fixed.WarrantyId, view.dropDowns.Warranties)}}</label>
                        </span>
                    </li>
                </ul>

                <ul class="sales-info-list ul-wide">
                    <li class="">
                        <span class="sales-list-left" title="Proforma invoice">Proforma invoice:</span>
                        <span id="span_ProformaInvoiceAutoName" class="sales-list-right">{{model.Reloadable.ProformaInvoice.Name}}</span>
                        {{#if !view.isInputEditable(view.isInvoiceClosed, model.Fixed) || !view.isProformaGenerationEnabled(model.Reloadable, model.Fixed)}}
                        <button type="button"
                                class="button btnSelectClass proforma-new fa fa-rotate-left disabled"
                                title="Create a new Proforma invoice"></button>
                        {{else}}
                        <button type="button"
                                class="button btnSelectClass proforma-new fa fa-rotate-left"
                                title="Create a new Proforma invoice"
                                on-click="createNewProformaInvoice"></button>
                        {{/if}}
                    </li>
                    <li class="">
                        <span class="sales-list-left">Order Type:</span>
                        <span class="sales-list-right">
                            <select value="{{model.Fixed.OrderTypeId}}" on-change="saveSalesOrder"
                                    {{#if !view.isOrderTypeEditable(view.isInvoiceClosed, model.Fixed, model.Reloadable)}} disabled="disabled" {{/if}}>
                                {{#view.dropDowns.OrderSubjectTypes}}
                                <option value="{{value}}">{{label}}</option>
                                {{/view.dropDowns.OrderSubjectTypes}}
                            </select>
                        </span>
                    </li>
                    <!-- CHECK -->
                    <!--<li class="" id="li_InboundRepairPO"></li>-->
                    {{#if view.isOutboundRepairSo(model.Fixed.SalesOrderId, model.Fixed.OrderTypeId)}}
                    <repairpolink on-generated="refreshReloadable"
                                  SalesOrderId="{{model.Fixed.SalesOrderId}}"
                                  model="{{model.Reloadable.RepairPo}}" />
                    {{/if}}
                    <li>
                        <span class="sales-list-left">Source Type:</span>
                        <span class="sales-list-right">
                            <select value="{{model.Reloadable.OrderSourceId}}"
                                    on-change="saveSalesOrder"
                                    {{#if !view.isOrderTypeEditable(view.isInvoiceClosed, model.Fixed, model.Reloadable)}} disabled="disabled" {{/if}}>
                                {{#each view.orderSources()}}
                                <option value="{{value}}">{{label}}</option>
                                {{/each}}
                            </select>
                        </span>
                    </li>

                    <li class="">
                        <span class="sales-list-left">Warehouse:</span>
                        <span class="sales-list-right">
                            <select id="dd_Warehouse" value="{{model.Fixed.WarehouseId}}" on-change="saveSalesOrder"
                                    {{#if !view.isInputEditable(view.isInvoiceClosed, model.Fixed)}} disabled="disabled" {{/if}}>
                                {{#if model.Fixed.WarehouseId == 0}}
                                <option value="0" disabled="disabled">Select Warehouse</option> {{/if}}>
                                {{#view.dropDowns.Warehouses}}
                                <option value="{{value}}">{{label}}</option>
                                {{/view.dropDowns.Warehouses}}
                            </select>
                        </span>
                    </li>
                    <li>
                        <span class="sales-list-left">Finance Release:</span>
                        <span class="sales-list-right">
                            <select value="{{model.Fixed.IsFinanceRelease}}" on-change="saveSalesOrder"
                                    {{#if !view.isInputEditable(view.isInvoiceClosed, model.Fixed)}} disabled="disabled" {{/if}}>
                                <option value="1">Released</option>
                                <option value="0">Not released</option>
                            </select>
                        </span>
                    </li>
                </ul>

                <ul class="sales-info-list ul-wide">
                    <li {{#if model.Fixed.IsFromMagento}} title="Cannot change the currency of eBay/Magento orders" {{/if}}>
                        <span class="sales-list-left">Currency:</span>
                        <div id="div_CurrencySelector" class="sales-list-right">
                            <CurrencySelector currencyExchangeId="{{model.Reloadable.CurrencyExchangeId}}"
                                              disabled="{{!view.isCurrencyEditable(view.isInvoiceClosed, model)}}"
                                              on-currencychanged="currencyChanged"></CurrencySelector>
                        </div>
                    </li>
                    <li>
                        <input type="hidden" for="taxes" value="{{model.Reloadable.TaxId}}" lazy="false" on-change="SaveTax"/>
                        {{#if model.Reloadable.IsTaxExempt }}
                        <span class="sales-list-left">Tax (exempt):</span>
                        {{elseif model.Fixed.AutoApplySalesTax}}
                        <span class="sales-list-left">Tax (auto apply):</span>
                        {{else}}
                        <span class="sales-list-left">Tax:</span>
                        {{/if}}
                        <span class="sales-list-right">
                            <input type="text"
                                   id="taxes"
                                   class="left ac-input"
                                   value="{{model.Reloadable.TaxLabel}}"
                                   title="The system gets and uses item-level tax values by default during invoice generation, if Avalara is enabled. The tax dropdown is optional — selecting a tax here will apply that rate to all items and override their individual tax settings"
                                   decorator="autocomplete:{{view.autocomplete.taxes.dataSource}}, {{view.autocomplete.taxes.options}}, {{view.autocomplete.taxes.events}}"
                                   {{#if view.isTaxInputsDisabled(view.isInvoiceClosed, model.Fixed, model.Reloadable.IsTaxExempt, model.Reloadable.TaxId) }} disabled="disabled" {{/if}}/>
                        </span>
                    </li>
                    <li>
                        <label for="cb_TaxShippingCost" class="sales-list-left">Tax Shipping:</label>
                        <span class="sales-list-right">
                            <input type="checkbox"
                                   id="cb_TaxShippingCost"
                                   class="left ac-input"
                                   checked="{{model.Reloadable.IsApplyTaxToShippingCost}}"
                                   on-change="saveSalesOrder"
                                   {{#if view.isTaxInputsDisabled(view.isInvoiceClosed, model.Fixed, model.Reloadable.IsTaxExempt, model.Reloadable.TaxId) }} disabled="disabled" {{/if}}/>
                            <label for="cb_TaxShippingCost">&nbsp;{{view.currencyMark}}{{{view.format.amountToFixed(model.Reloadable.ShippingTax)}}}</label>
                        </span>
                    </li>
                    <li>
                        <label for="cb_TaxMiscCharge" class="sales-list-left">Tax Misc Charge:</label>
                        <span class="sales-list-right">
                            <input type="checkbox"
                                   id="cb_TaxMiscCharge"
                                   class="left ac-input"
                                   checked="{{model.Reloadable.IsApplyTaxToMiscCharge}}"
                                   on-change="saveSalesOrder"
                                   {{#if view.isTaxInputsDisabled(view.isInvoiceClosed, model.Fixed, model.Reloadable.IsTaxExempt, model.Reloadable.TaxId) }} disabled="disabled" {{/if}}/>
                            <label for="cb_TaxMiscCharge">&nbsp;{{view.currencyMark}}{{{view.format.amountToFixed(model.Reloadable.MiscChargeTax)}}}</label>
                        </span>
                    </li>

                    <li class="">
                        <span class="sales-list-left plus">Cost:</span><span style="float: left;">{{view.currencyMark}}</span>
                        <span id="span_Cost" class="sales-list-right">{{{view.format.amountToFixed(model.Reloadable.Cost)}}}</span>
                    </li>
                    <li class="">
                        <span class="sales-list-left plus">Gross Profit:</span><span style="float: left;">{{view.currencyMark}}</span>
                        <span id="span_GrossProfit" class="sales-list-right">{{{view.format.amountToFixed(model.Reloadable.GrossProfit)}}}</span>
                    </li>
                </ul>
            </div>

                <div class="wrap-total-block">
                </div>

                <div class="wrap-total-block">

                    <h4 class="status-pending"><span>STATUS:</span> <span class="sales-order-status">{{model.Reloadable.StatusName}}</span></h4>

                    <ul class="total-block-list">
                        <li>
                            <span class="total-list-left">Subtotal</span><span style="float: left;">{{view.currencyMark}}</span>
                            <span id="span_Subtotal" class="total-list-right">&nbsp;{{{view.format.amountToFixed(model.Reloadable.Subtotal)}}}</span>
                        </li>
                        <li>
                            <span class="total-list-left">Tax</span><span style="float: left;">{{view.currencyMark}}</span>
                            <span id="span_Tax" class="total-list-right">&nbsp;{{{view.format.amountToFixed(model.Reloadable.Tax)}}}</span>
                        </li>
                        <li>
                            <span class="total-list-left">Shipping</span><span style="float: left;">{{view.currencyMark}}</span>
                            <input type="number" class="short-input" on-change="saveSalesOrder" value="{{model.Reloadable.ShippingCost}}" step="0.01"
                                   {{#if !view.isInputEditable(view.isInvoiceClosed, model.Fixed)}} disabled="disabled" {{/if}} />
                        </li>
                        <li>
                            <span class="total-list-left">Misc Charge</span><span style="float: left;">{{view.currencyMark}}</span>
                            <input type="number" class="short-input" on-change="saveSalesOrder" value="{{model.Reloadable.MiscCharge}}" step="0.01"
                                   {{#if !view.isInputEditable(view.isInvoiceClosed, model.Fixed)}} disabled="disabled" {{/if}} />
                        </li>
                        <li>
                            <span class="total-list-left">Payments Applied</span><span style="float: left;">{{view.currencyMark}}</span>
                            <span id="span_PaymentsApplied_totals" class="total-list-right">&nbsp;{{{view.format.amountToFixed(model.Reloadable.PaymentsApplied)}}}</span>
                        </li>
                        <li class="ammount-due">
                            <span class="total-list-left">Amount Due</span><span style="float: left;">{{view.currencyMark}}</span>
                            <span id="span_AmountDue" class="total-list-right {{#if model.Reloadable.AmountDue < 0}} cost-negative {{/if}}">
                                &nbsp;{{{view.format.amountToFixed(model.Reloadable.AmountDue)}}}
                            </span>
                        </li>
                    </ul>
                </div>

            </div>
            <!-- Tabs -->
            <div class="wrap-tabs-table " id="tabs" style="margin-top: -20px !important; width: 99.2%">
                <ul class="tabs-table-title">
                    {{#each logic.TabControls: i}}
                    <li on-click="tabSwitching: {{i}}" {{#if (!view.havePermissionToSeeOrderAdminTab() && i == 4)}} style="display:none;" {{/if}} class="tabs-table-item {{#if (logic.ActiveTabIndex == i)}}ui-tabs-active ui-state-active{{/if}}">
                        {{this.GetCaption(model.Reloadable)}}
                    </li>  
                    {{/each}}                  
                </ul>
                
                <div>
                    {{#each logic.TabControls: i}}
                    <div id="{{this.ContainerId}}" class="tabs-table-content clearfix" {{#if (logic.ActiveTabIndex != i)}} style="display:none;" {{/if}} ></div>
                    {{/each}}
                </div>
            </div>

        </div>

        <div class="wrap-tabs-sales-order rightInfoTab">

            <a href="#" class="wrap-tabs-sales-order-toggle-panel__toggle-panel-hide toggleBtn"></a>

            <div class="tabs-order flw" style="margin-top:33px;">
                <div id="tab_Customer" class="tabs-item">
                    <h4 class="active">CUSTOMER</h4>
                </div>
            </div>

            <div id="tabCustomerContent" class="tabs-sales-content">

                <div class="tabs-sales-item">
                    <h5 class="tabs-sales-title" data-id="tg-activity_log">Activity Log <i class="fa fa-chevron-down"></i></h5>
                    <div id="tg-activity_log">
                        <!-- Original RMA -->
                        {{#if model.Fixed.SourceRmaId > 0}}
                        <ul class="tabs-sales-list clearfix">
                            <li class="clearfix">
                                <span class="tabs-sales-span-left">RMA #{{{view.format.rmaLink(model.Fixed.SourceRmaId, model.Fixed.SourceRmaStatusId)}}}</span>
                                <span class="tabs-sales-span-right">{{{view.format.salesOrderLink(model.Fixed.SourceRmaSalesOrderId, model.Fixed.SourceRmaSalesOrderAutoName)}}}&nbsp;</span>
                            </li>
                        </ul>
                        {{/if}}

                        <!-- Linked Purchase orders -->
                        {{#if model.Reloadable.PurchaseOrders.length > 0}}
                        <ul class="tabs-sales-list clearfix">
                            {{#model.Reloadable.PurchaseOrders}}
                            <li class="clearfix">
                                <span class="tabs-sales-span-left">{{{view.format.purchaseOrderLink(PoId, PoAutoName)}}}</span>
                                <span class="tabs-sales-span-right">{{{view.format.customerLink(CustomerId, CustomerName)}}}</span>
                                <span class="tabs-sales-span-right gray">({{StatusName}})</span>
                            </li>
                            {{/model.Reloadable.PurchaseOrders}}
                        </ul>
                        {{/if}}

                        <!-- RMAs -->
                        {{#if model.Reloadable.Rmas.length > 0}}
                        <ul class="tabs-sales-list clearfix">
                            {{#model.Reloadable.Rmas}}
                            <li class="clearfix">
                                <span class="tabs-sales-span-left">RMA #{{{view.format.rmaLink(this.Id, this.Status)}}}</span>
                            </li>
                            {{/model.Reloadable.Rmas}}
                        </ul>
                        {{/if}}

                        <!-- Internal RMAs -->
                        {{#if model.Reloadable.HasInternalRmas}}
                        <ul class="tabs-sales-list clearfix">
                            <li class="clearfix">
                                <span class="tabs-sales-span-left">Duplicate item was sold.</span>
                            </li>
                        </ul>
                        {{/if}}

                        <!-- Pick List -->
                        {{#if model.Fixed.PickLists.length > 0}}
                        <ul class="tabs-sales-list clearfix">
                            {{#model.Fixed.PickLists}}
                            <li class="clearfix">
                                <span class="tabs-sales-span-left">{{{view.format.pickListLink(this.Id)}}} ({{{view.format.picklistStatus(this.StatusId)}}})</span>
                            </li>
                            {{/model.Fixed.PickLists}}
                        </ul>
                        {{/if}}

                        <!-- AppliedPayments/CreditMemo -->
                        {{#if model.AppliedPayments.length > 0}}
                        <ul class="tabs-sales-list clearfix">
                            {{#model.AppliedPayments}}
                            <li class="clearfix">
                                <span class="tabs-sales-span-left">Payment #{{{this.PaymentId}}} {{#if this.CreditMemoAutoName}} ({{{this.CreditMemoAutoName}}}) {{/if}}</span>
                            </li>
                            {{/model.AppliedPayments}}
                        </ul>
                        {{/if}}
                    </div>
                </div>
                <div class="tabs-sales-item">
                    <h5 class="tabs-sales-title __block-items" data-id="tg-tracking_numbers">Shipment Log <i class="fa fa-chevron-down"></i></h5>
                    <div id="tg-tracking_numbers">
                        <!-- Tracking numbers -->
                        {{#each model.ShippingLog: s}}
                        <h6 class="tabs-sales_item-title clearfix">Shipping #{{ShippingId}}:</h6>
                        <ul class="tabs-sales-list __block-items clearfix">
                            {{#each Packages: p}}
                            <li class="tabs-sales-list_item clearfix {{view.format.liExpanderClass(Expanded)}}">
                                {{#if TrackingNumber }}
                                <a target="_blank" href="{{view.format.trackingLink(ProviderId, TrackingNumber)}}" alt="link to tracking details">{{TrackingNumber}}</a>
                                {{/if}}
                                <span on-click="expandCollapseShippingEvent:{{Expanded}},{{s}},{{p}}" class="package-status-span" title="{{Status}}">({{Status}}) <span class="open-details"><i class="fa {{view.format.expanderClass(Expanded)}}"></i></span></span>
                                <div>
                                    {{#if Events.length > 0}}
                                    <ul class="shipment-details">
                                        {{#each Events}}
                                        <li>{{this}}</li>
                                        {{/each}}
                                    </ul>
                                    {{/if}}
                                </div>
                            </li>
                            {{/each}}
                        </ul>
                        {{/each}}
                    </div>
                </div>

				{{#if view.isRequsitionRequest(model.Fixed.OrderTypeId)}}
				<div class="tabs-sales-item">
					<h5 class="tabs-sales-title" data-id="tg-shipto_sidebar">Ship to <i class="fa fa-chevron-down"></i></h5>
					<div class="tabs-sales-text" id="tg-shipto_sidebar">
						<p>{{model.Fixed.ClientShipToName}}</p>
						<p>{{model.Fixed.ClientShipToAddress}}</p>
					</div>
				</div>

				<div class="tabs-sales-item">
					<h5 class="tabs-sales-title" data-id="tg-billto_sidebar">Bill to <i class="fa fa-chevron-down"></i></h5>
					<div class="tabs-sales-text" id="tg-billto_sidebar">
						<p>{{model.Fixed.ClientBillToName}}</p>
						<p class="text-pre-wrap">{{model.Fixed.ClientBillToAddress}}</p>
					</div>
				</div>
				{{/if}}

                <div class="tabs-sales-item">
                    <!--<input type="checkbox" id="cb_CardProcessed" style="display: none;" />-->
                    <h5 class="tabs-sales-title" data-id="tg-Comments">Comments <i class="fa fa-chevron-down"></i></h5>
                    <div id="tg-Comments">
					    <textarea on-change="saveSalesOrder" value="{{model.Fixed.Comments}}"
							  {{#if !view.isInputEditable(false, model.Fixed)}} disabled="disabled" {{/if}}></textarea>
                    </div>
                    <h5 class="tabs-sales-title" data-id="tg-Internal_Comments">Internal Comments <i class="fa fa-chevron-down"></i></h5>
                    <div id="tg-Internal_Comments">
                        <textarea on-change="saveSalesOrder" value="{{model.Fixed.InternalComments}}"
                                  {{#if !view.isInputEditable(false, model.Fixed)}} disabled="disabled" {{/if}}></textarea>
                    </div>
                    <h5 class="tabs-sales-title" data-id="tg-Packing_Slip_Notes">Packing Slip Notes <i class="fa fa-chevron-down"></i></h5>
                    <div id="tg-Packing_Slip_Notes">
                        <textarea on-change="saveSalesOrder" value="{{model.Fixed.PackingSlipNotes}}"
                                  {{#if !view.isInputEditable(false, model.Fixed)}} disabled="disabled" {{/if}}></textarea>
                    </div>
                </div>

                <div class="tabs-sales-item">
					{{#if model.Fixed.SalesOrderId}}
                    <h5 class="tabs-sales-title" data-id="tg-Attachments">Attachments <i class="fa fa-chevron-down"></i></h5>
                    <div id="tg-Attachments">
                        <iframe id="if_UploaderSide" src="{{view.fileUploadUrl(model.Fixed.SalesOrderId)}}" style="width: 100%; height: auto; min-height:600px; border:none; "></iframe>
                    </div>
					{{/if}}
                </div>
            </div>
            
        </div>
    </div>
</div>