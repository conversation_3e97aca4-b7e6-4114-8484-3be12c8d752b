function OutboundTabPreliminary() {
}; // : ITabControl

OutboundTabPreliminary.prototype = {
    _urls: {
        getActiveTab: Common.baseUrl + '/Services/OutboundOrdersService.asmx/GetOutboundShippingMethod'        
    },

    _secondaryData:{

    },

    Events: {
        NeedSave: null,
    },

    // ITabControl implementation ----------------------------------------------------
    GetData: function() {
        var it = this;
        var data = it.getActiveControl().GetData();
        return data;
    },

    Validate: function (actionOptions) {
        var it = this;
        var isValid = it.getActiveControl().Validate(actionOptions);
        return isValid;
    },
    
    reset: function () {
        var it = this;
        it._controls.truck.Reset();
        it._controls.container.Reset();
        $(it._sel.rbTruck).prop('checked', true);
    },

    createControls: function () {
        var it = this;
        it._sel = {
            divCurrency: '#div_CurrencySelector',
            rbTruck:     '#rb_Outbound_ScheduleTruck',
            rbContainer: '#rb_Outbound_ScheduleContainer',
            reportButtons: "#div_Outbound_Reports"
        };

        it._controls.currency = new Ractive.components.CurrencySelector({ el: it._sel.divCurrency });
        if (it._currencyExchangeId) {
            it.SetCurrency(it._currencyExchangeId, it._statusId);
        } else {
            it.SelectFirstCurrency();
        }

        if ($(it._sel.rbTruck).getRadioGroup().indexOfChecked() == -1) {
            $(it._sel.rbTruck).prop('checked', true);
        }

        it._controls.truck     = new TruckSchedulingControl({ statusId: it._statusId });
        it._controls.container = new ContainerSchedulingControl({ statusId: it._statusId });

        it._controls.truck.Events.NeedSave = function (onSaved) {
            invokeEvent(it.Events.NeedSave, onSaved);
        };
        it._controls.container.Events.NeedSave = function (onSaved) {
            invokeEvent(it.Events.NeedSave, onSaved);
        };

        it._controls.truck.Events.CheckIsChanged = function (onGoOn) {
            const isChanged = it.IsCurrencyChanged();
            onGoOn(isChanged);
        };
        it._controls.container.Events.CheckIsChanged = function (onGoOn) {
            const isChanged = it.IsCurrencyChanged();
            onGoOn(isChanged);
        };

        // bind events
        $(it._sel.rbTruck).getRadioGroup().change(function(event) {
            it.getActiveControl().Load(it._data.OrderId);
            it.AppendPickupAndShipToAddresses(it._secondaryData, $(it._sel.rbTruck).is(':checked'));
            it.toggleActiveControl();
        });

        $.each([it._controls.truck, it._controls.container], function (key, control) {
            if (control) {
                control.InvokeSave = function (onCanGoOn) {
                    it.saveAndContinueWith(onCanGoOn);
                }
            }
        });
    },

    loadData: function (dataId, afterLoading, secondaryData, forceLoad) {
        var it = this;
        it._data.OrderId = Number.parse(dataId, null);

        // toggle the saved control on
        if (Number.isNumber(it._data.OrderId)) {
            var control = it.getActiveControl();
            if (!control.IsSuspended(it._data.OrderId)) {
                var response = Url.PostSync(it._urls.getActiveTab, { orderId: it._data.OrderId });
                ProcessAjaxItemResponse(response, function (data) {
                    $(it._sel.rbTruck).prop('checked', data.IsTruck);
                    $(it._sel.rbTruck).getRadioGroup().not(it._sel.rbTruck).prop('checked', !data.IsTruck); // no change event here!

                    $(it._sel.rbContainer).prop('disabled', data.IsTransfer);
                    $.extend(true, secondaryData, data);
                });
            }
        } else {
            it._controls.currency.SelectFirstCurrency();
            if (secondaryData.IsTransfer) {
                $(it._sel.rbContainer).prop('disabled', true);
            } else {
                $(it._sel.rbContainer).prop('disabled', false);
            }
        }

        it._secondaryData = secondaryData;

        // load the active control
        if (it.getActiveControl().Load(it._data.OrderId, forceLoad) && $.isFunction(afterLoading)) { // SYNC !!!
            afterLoading();
        }
        if ($(it._sel.rbTruck).is(':checked')) {
            it.AppendPickupAndShipToAddresses(secondaryData, it._data == null ? true : false);
        }

        it.OnCustomerChanged(secondaryData);

        it.toggleActiveControl();
    },

    saveAndContinueWith: function (fnOnContinue) {
        var it = this;
        ShowConfirm("You have unsaved changes. Do you want to save?", null, function () {
            it.saveData('Save', it._data.OrderId, function () {
                fnOnContinue(it._data.OrderId);
            });
        }, function () {
            fnOnContinue(it._data.OrderId);
        });
    },

    AppendPickupAndShipToAddresses: function(data, isReloadRequired){
        var it = this;
        if (it._secondaryData) {
            it._secondaryData.CustomerId = data.CustomerId;
            it._secondaryData.CustomerName = data.CustomerName;
        }

        it._controls.truck.AppendPickupAddress(data, isReloadRequired);
    },

    OnCustomerChanged: function (data) {
        const it = this;
        if (it._controls.truck) {
            it._controls.truck.OnCustomerChanged(data);
        }

        if (it._controls.container) {
            it._controls.container.OnCustomerChanged(data);
        }
    },

    GetCurrency: function () {
        const it = this;
        return it._controls && it._controls.currency && it._controls.currency.GetData();
    },

    SetCurrency: function (currencyExchangeId, statusId) {
        const it = this;
        it._currencyExchangeId = currencyExchangeId;
        it._statusId = statusId;
        if (!it._controls || !it._controls.currency)
            return;

        it._controls.currency.SetSelectClass('currency-selector-for-dialogs');
        it._controls.currency.SetData(currencyExchangeId);
        const canModifyCurrency = statusId != RecyclingOrderStatuses.Settled
            && statusId != RecyclingOrderStatuses.Canceled;
        it._controls.currency.ToggleEnabled(canModifyCurrency);
    },

    SelectFirstCurrency: function () {
        const it = this;
        if (!it._controls || !it._controls.currency)
            return;
        it._controls.currency.SelectFirstCurrency();
    },

    IsCurrencyChanged: function () {
        const it = this;
        const old = JSON.stringify(it._currencyExchangeId);
        const curr = JSON.stringify(it.GetCurrency()?.CurrencyExchangeId);
        return old !== curr;
    },

    OnOrderInfoLoaded: function (orderInfo) {
        const it = this;

        if (it._controls?.truck) {
            it._controls.truck.OnOrderInfoLoaded(orderInfo);
        }

        if (it._controls?.container) {
            it._controls.container.OnOrderInfoLoaded(orderInfo);
        }

        it._statusId = orderInfo.StatusId;
    },

    saveData: function(saveAs, dataId, afterSaving) {
        var it = this;
        it._data.OrderId = Number.parse(dataId, null); // update id        
        it._currencyExchangeId = it.GetCurrency()?.CurrencyExchangeId;

        // filter out the void actions
        if (saveAs == OutboundOrderActions.Delete || saveAs == OutboundOrderActions.Close) {
            callIfFunction(afterSaving, saveAs == OutboundOrderActions.Delete);
            return;
        }

        it.getActiveControl().Save(it._data.OrderId, function () {
            // reload data after saving
            it.loadData(dataId, function () {
                it._state = JSON.stringify(it.GetData());
                callIfFunction(afterSaving, true);
            }, it._secondaryData, true);
        });
    },
    // ---------------------------------------------------- ITabControl implementation

    getActiveControl: function() {
        var it = this;
        var control;
        control = $(it._sel.rbTruck).is(':checked')
            ? it._controls.truck
            : it._controls.container;
        return control;
    },

    toggleActiveControl: function () {
        var it = this;
        if ($(it._sel.rbTruck).is(':checked')) {
            it._controls.truck.Show();
            it._controls.container.Hide();
        } else {
            it._controls.container.Show();
            it._controls.truck.Hide();
        }
    },

    suspend: function () {
        var it = this;
        it.getActiveControl().Suspend();
    },

    resetSuspended: function () {
        var it = this;
        it.getActiveControl().ResetSuspended();
    },

    stateHasChanged: function () {
        return true; // need for the Suspend function
    }
};
// inheriting ITabControl
OutboundTabPreliminary.prototype = $.extend({}, ITabControl.prototype, OutboundTabPreliminary.prototype);