/*
    "Charge" goes for negative values. It means, that the customer will have to pay, so TBS will charge
*/

function GridServiceItems(params) {
    this._initParams = params ? params : true;
};

GridServiceItems.prototype = {
    _options: {
        minPrice: -1000000,
        maxPrice: 1000000,
        maxQty: 1000000,
        priceDecimals: 4,
        subtotalDecimals: 2
    },

    _urls: {
        del: Common.baseUrl + '/Services/RecyclingOrderItemsService.asmx/ServiceItemsDelete',
        save: Common.baseUrl + '/Services/RecyclingOrderItemsService.asmx/ServiceItemSave',
        list: Common.baseUrl + '/Services/RecyclingOrderItemsService.asmx/ListItemServiceTypes',
        loadGrid: Common.baseUrl + '/Services/RecyclingOrderItemsService.asmx/SettlementServicessLoad',
        loadDetails: Common.baseUrl + '/Services/RecyclingOrderItemsService.asmx/SettlementServicesDetailsLoad',
        subtotal: Common.baseUrl + '/Services/RecyclingOrderItemsService.asmx/GetOrderServicesSubtotal',
        listPriceTypes: Common.baseUrl + '/Services/ContractService.asmx/ListPriceTypes',
        updatDetail: Common.baseUrl + '/Services/RecyclingOrderItemsService.asmx/UpdateOnsiteServiceDetail',
        loadMasterRow: Common.baseUrl + '/Services/RecyclingOrderItemsService.asmx/SettlementServiceLoad',
        getServicePriceByQty: Common.baseUrl + '/Services/RecyclingOrderItemsService.asmx/GetServicePricebyQty',
    },

    _sel: {
        divButtons: '#div_SettlementServices_Buttons',
        acServices: '#ac_SettlementServices',
        spQty: '#sp_SettlementServices_Qty',
        spPrice: '#sp_SettlementServices_Price',
        ddPriceType: '#dd_SettlementServices_PriceType',
        taNotes: '#ta_SettlementServices_Notes',
        grid: '#g_SettlementServices',
        spanPrice: '#span_SettlementServices_Price',
        spanHomeCurrencyPrice: '#span_SettlementServices_HomeCurrencyPrice',
        spEditQty: '#sp_SettlementServices_RowQty',
        spEditPrice: '#sp_SettlementServices_RowPrice',
        ckHideZeroPriceServices: '#ck_HideZeroPriceServices'
    },

    _data: {
        RecyclingOrderId: null,
        settlerStatusId: null,
        servicesList: []
    },

    _controls: {
        menu: null,
        grid: null,
        acServices: null,
        gridColumnDef: null
    },

    getBaseColumnDef: function () {
        const it = this;

        const isEditable = () => it.canEdit();

        if (!it._data.gridColumnDef) {

            it._data.gridColumnDef = [
                { field: 'ServiceItemId', hide: true, key: true },
                { width: 40, checkboxSelection: true, sortable: false, filter: false, pinned: 'left' },
                { headerName: 'Item', field: 'ServiceTypeCd', width: 180, cellRenderer: 'agGroupCellRenderer' },
                { field: 'ApplyTypeId', hide: true },
                {
                    headerName: 'Description', field: 'Descr', width: 220, editable: isEditable,
                    valueSetter: function (params) {
                        return it.beforeSelect(params);
                    },
                    onCellValueChanged: function (eventParams) {
                        if (eventParams.newValue !== eventParams.oldValue) {
                            it.saveData(eventParams);
                        }
                    }
                },
                {
                    headerName: 'Qty', field: 'Qty', width: 110, type: 'number',
                    cellEditor: NumericAgCellEditor,
                    cellEditorParams: {
                        min: 0,
                        max: it._options.maxQty,
                        precesion: 0
                    },
                    valueSetter: function (params) {
                        return it.beforeSelect(params);
                    },
                    onCellValueChanged: function (eventParams) {
                        if (eventParams.newValue !== eventParams.oldValue) {
                            it.saveData(eventParams);
                        }
                    },
                    editable: function (params) {
                        return isEditable() && !params.data?.HasDetails;
                    },
                    cellClassRules: {
                        'disabled-cell': (params) => params.data?.HasDetails
                    }
                },
                { headerName: 'Qty Calculated', field: 'QtyCalculated', width: 110, type: 'number' },
                { headerName: 'For Commodity', field: 'CommodityName', width: 110 },
                {
                    headerName: 'Price', field: 'Price', width: 135, editable: isEditable, type: "number",
                    cellRenderer: ContextCurrencyAgCellRenderer,
                    cellRendererParams: {
                        isHightlightNegative: true,
                        context: () => RecyclingOrderContext
                    },
                    cellEditor: NumericAgCellEditor,
                    cellEditorParams: {
                        min: it._options.minPrice,
                        max: it._options.maxPrice,
                        precesion: 4
                    },
                    valueSetter: function (params) {
                        return it.beforeSelect(params);
                    },
                    onCellValueChanged: function (eventParams) {
                        if (eventParams.newValue !== eventParams.oldValue) {
                            it.saveData(eventParams);
                        }
                    }
                },
                (new PayChargeAgColumnDefBuilder('Price')).useColumnDef({ headerName: 'Pay/Charge', width: 100, filter: false, sortable: false }).build(),
                {
                    headerName: 'Price Type', field: 'PriceTypeDesc', width: 90, sortable: true,
                    editable: isEditable,
                    cellEditor: 'agRichSelectCellEditor',
                    cellEditorParams: function (params) {
                        let availableOptions = _.pluck(it._priceTypes, 'label');
                        if (params.data && params.data.ApplyTypeId == 6) {
                            const weightOption = _.find(it._priceTypes, { value: 1 });
                            if (weightOption) {
                                availableOptions = _.without(availableOptions, weightOption.label);
                            }
                        }
                        return {
                            values: availableOptions
                        };
                    },
                    onCellValueChanged: function (eventParams) {
                        if (eventParams.newValue !== eventParams.oldValue) {
                            const priceType = _.find(it._priceTypes, { label: eventParams.newValue });
                            if (priceType) {
                                eventParams.data.PriceTypeId = priceType.value;
                                it.saveData(eventParams);
                            }
                        }
                    },
                    filter: 'agSetColumnFilter',
                    filterParams: {
                        newRowsAction: 'keep',
                        values: _.pluck(it._priceTypes, 'label')
                    }
                },
                (new CurrencyAgColumnDefBuilder(true, 2, true, true, true, ContextCurrencyAgCellRenderer, { context: () => RecyclingOrderContext }))
                    .chainColumnDefBuilder(new SubtotalAgColumnDefBuilder('Price', 'Qty', 'Qty', 'PriceTypeId'))
                    .useColumnDef({ headerName: 'Subtotal', width: 110, filter: false, sortable: false }).build(),
                {
                    headerName: 'Notes', field: 'Notes', width: 210, editable: isEditable,
                    valueSetter: function (params) {
                        return it.beforeSelect(params);
                    },
                    onCellValueChanged: function (eventParams) {
                        if (eventParams.newValue !== eventParams.oldValue) {
                            it.saveData(eventParams);
                        }
                    }
                },
                { headerName: 'HasDetails', field: 'HasDetails', hide: true }
            ];
        }

        return it._data.gridColumnDef;
    },

    Events: {
        Changed: [], //calc common info: weights, prices, count
        PriceChanged: [] //calc general subtotal price
    },

    Load: function (recyclingOrderId) {
        const it = this;

        it._data.RecyclingOrderId = Number.parse(recyclingOrderId, null);

        if (it._initParams) {
            it.init();
            it._initParams = null;
        }

        it._controls.menu.ToggleEnabled({ Add: true });
    },

    calculateSubtotal: function (castEvent) {
        const it = this;
        Url.PostAsync(it._urls.subtotal, {
            recyclingOrderId: it._data.RecyclingOrderId,
            isOutbound: it._isOutbound
        }, function (response) {
            ProcessAjaxItemResponse(response, function (value) {
                $(it._sel.spanPrice).payChargeSetValue(value.foreignCurrency);
                if (RecyclingOrderContext.IsInForeignCurrency()) {
                    $(it._sel.spanHomeCurrencyPrice).payChargeSetValue(value.homeCurrency, UserSettings.GetCurrencyMark(), "({0})");
                }
            });
        });
        if (castEvent) {
            invokeEvent(it.Events.Changed);
        }
    },

    getServicePrice: function () {
        const it = this;
    
        const rowData = {
            RecyclingOrderId: it._data.RecyclingOrderId,
            ServiceTypeId: it._controls.acServices.GetDataId(),
            Qty: $(it._sel.spQty).val(),
            Price: $(it._sel.spPrice).val() || 0,
            PriceTypeId: $(it._sel.ddPriceType).val(),
        };

        var response = Url.PostSync(it._urls.getServicePriceByQty, { service: rowData });
        ProcessAjaxItemResponse(response, function (data) {
            if (data) {
                $(it._sel.spPrice).val(data.foreignCurrency);
            }
        });
    },

    init: function () {
        const it = this;
        it._isOutbound = it._initParams.isOutbound === true;
        it._controls.dialogPassword = it._initParams.dialog;

        it.loadPriceTypes();

        // top controls
        it._controls.acServices = new UnifiedAutocomplete(it._sel.acServices, it._urls.list, {
            discardUnknown: true, restoreIfEmpty: true
        }, {
            beforeSearch: function (params) {
                params.recyclingOrderId = it._data.RecyclingOrderId;
            },
            onChange: function () {
                var qty = $(it._sel.spQty).val();
                var serviceId = it._controls.acServices.GetDataId();
                if (serviceId != null && !String.isNullOrEmpty(qty))
                    it.getServicePrice();
                
                it.filterPriceTypeOptions();
            },
            onDataLoad: function (data) {
                it._data.servicesList = data || [];
            }
        });

        $(it._sel.spanPrice).LabelPayCharge({ pay: 'Pay:', charge: 'Charge:', hideLabelIfZero: true, precesion: it._options.subtotalDecimals, mark: RecyclingOrderContext.GetCurrencyMark() });

        $(it._sel.ckHideZeroPriceServices).on('change', function () {
            it.ReloadGrid();
        });

        $(it._sel.ddPriceType).rebuildSelectOptions(it._priceTypes);

        $(it._sel.spQty).on('change', function () {
            var qty = $(it._sel.spQty).val();

            if (!String.isNullOrEmpty(qty) && it._controls.acServices.GetDataId() != null)
                it.getServicePrice();
        });

        // menu
        it._controls.menu = new ActionButtons({
            onAdd: function (event) {
                event.preventDefault();
                const btn = this;
                if ($(it._sel.acServices).ValidateAutocomplete(true, 'Item') &&
                    $(it._sel.spQty).ValidateNumeric(true, 'Qty', true, false) &&
                    $(it._sel.spPrice).ValidateNumeric(true, 'Price', false, true) &&
                    $(it._sel.taNotes).ValidateLength(false, 512, 'Notes')) {

                    const selectedServiceTypeId = it._controls.acServices.GetDataId();
                    const selectedPriceTypeId = $(it._sel.ddPriceType).val();
                
                    if (it.isServiceWithApplyType6(selectedServiceTypeId) && selectedPriceTypeId == 1) {
                        ShowAlert('Cannot select "For Weight" price type for this service type.');
                        return;
                    }

                    const rowData = {
                        RecyclingOrderId: it._data.RecyclingOrderId,
                        ServiceId: null,
                        ServiceTypeId: selectedServiceTypeId,
                        Qty: $(it._sel.spQty).val(),
                        Price: $(it._sel.spPrice).val(),
                        PriceTypeId: selectedPriceTypeId,
                        Notes: $(it._sel.taNotes).val()
                    };

                    Url.PostAsync(it._urls.save, { service: rowData }, function () {
                        it.ReloadGrid();
                        it.calculateSubtotal(true);
                        it._controls.acServices.Clear();
                        $(it._sel.spQty).val(1);
                        $(it._sel.spPrice).val(0);
                        $(it._sel.ddPriceType).val(2); // Unit
                        $(it._sel.taNotes).val('');
                        invokeEvent(it.Events.Changed);
                    }, btn);
                }
            },
            onDelete: function (event) {
                event.preventDefault();
                var btn = this;
                var rows = it._controls.grid.getSelectedRows();
                if (rows.length > 0) {
                    ShowConfirm('The selectes service will be removed.<br/>Continue?', null, function () {
                        const serviceIds = _.chain(rows).filter('ServiceItemId').pluck('ServiceItemId').value();
                        Url.PostAsync(it._urls.del, { serviceIds: serviceIds, isOutbound: it._isOutbound }, function () {
                            it.ReloadGrid();
                            it.calculateSubtotal(true);
                            invokeEvent(it.Events.Changed);
                        }, btn);
                    });
                }
            },
            onSyncCalculated: function (event) {
                event.preventDefault();
                var btn = this;

                ShowConfirm('Do you want update manually changed quantites.<br/>Continue?', null,
                    function () {
                        Url.PostAsync('/recycling/api/RecyclingOrderItemServices/SyncCalculated',
                            {
                                RecyclingOrderId: it._data.RecyclingOrderId,
                                UpdateCountsWithCalculated: true,
                                CalculateOnlyAssetsInFinalSteps: false
                            },
                            function (response) {
                                ProcessAjaxItemResponse(response, function (data) {
                                    if (data) {
                                        it.ReloadGrid();
                                    }
                                });
                            },
                            btn);
                    },
                    function () {
                        Url.PostAsync('/recycling/api/RecyclingOrderItemServices/SyncCalculated',
                            {
                                RecyclingOrderId: it._data.RecyclingOrderId,
                                UpdateCountsWithCalculated: false,
                                CalculateOnlyAssetsInFinalSteps: false
                            },
                            function (response) {
                                ProcessAjaxItemResponse(response, function (data) {
                                    if (data) {
                                        it.ReloadGrid();
                                    }
                                });
                            },
                            btn);
                    }
                );
            }
        }, it._sel.divButtons);

        // grid
        it.initGrid();
    },

    loadPriceTypes: function () {
        const it = this;
        it._priceTypes = sessionDataStorage.GetSync(it._urls.listPriceTypes, false, true);

        const unitPriceType = _.find(it._priceTypes, { value: 2 });
        if (unitPriceType) {
            unitPriceType.selected = true; // Selected by default
        }
    },

    // status: inbound or outbound
    toggleButtons: function (settlerStatusId) {
        const it = this;
        if (!Number.isNumber(settlerStatusId)) {
            return;
        }
        it._data.settlerStatusId = settlerStatusId;
        const row = it.getSelectedRow();
        const isEnabled = it.canEdit();
        it._controls.menu.ToggleEnabled({
            Delete: isEnabled && row && row.ServiceItemId > 0,
            Add: isEnabled,
            SyncCalculated: isEnabled
        });
    },

    initGrid: function () {
        const it = this;

        const isEditable = () => it.canEdit();

        const filterLists = {
            PriceTypeDesc: {
                targetField: 'PriceTypeId',
                items: _.reduce(it._priceTypes, (d, i) => {
                    d[i.label] = i.value;
                    return d;
                }, {})
            }
        };

        it.datasource = new AgGridDataSource(it._urls.loadGrid, () => it.getGridData(), 'ServiceTypeCd', 'asc', filterLists);

        const options = {
            columnDefs: it.getBaseColumnDef(),

            masterDetail: true,
            rowModelType: 'serverSide',
            onSelectionChanged: onSelectionChanged,
            onGridReady: gridReady,


            detailCellRendererParams: {
                detailGridOptions: {
                    columnDefs: [
                        { headerName: 'Detail Name (Label/Category)', field: 'DetailName', width: 50 },
                        { headerName: 'EstimatedQty', field: 'EstimatedQty', width: 50, type: "number", editable: isEditable }
                    ],
                    defaultColDef: {
                        flex: 1,
                        sortable: true,
                        filter: true
                    },

                    onCellValueChanged: function (event) {
                        it.handleDetailCellChange(event);
                    }
                },
                getDetailRowData: params => {
                    params.data._parentNodeId = params.node.id;
                    if (params.data && params.data.ServiceTypeId && params.data.HasDetails) {
                        Url.PostAsync(it._urls.loadDetails, {
                            recyclingOrderId: it._data.RecyclingOrderId,
                            serviceTypeId: params.data.ServiceTypeId
                        },
                            function (data) {
                                if (data)
                                    params.successCallback(data.data);
                            });
                    }
                }
            },
            isRowMaster: function (dataItem) {
                return dataItem ? dataItem.HasDetails : false;
            },
        };

        it._controls.grid = new Ractive.components.agGrid({
            el: it._sel.grid,
            data: {
                id: 'ag-grid-settlement-services',
                exportUrl: Common.baseUrl + '/Services/RecyclingOrderItemsService.asmx/SettlementServicessExport',
                exportAndEmailUrl: Common.baseUrl + '/Services/RecyclingOrderItemsService.asmx/SettlementServicessExportEmail',
                height: '600px'
            },
            dataSource: it.datasource,
            options: options,
            settingsName: 'ag-Grid Settlement Services Grid'
        });

        // scope functions ---------------------
        function gridReady(event) {
            it.calculateSubtotal(false);
            it.toggleButtons(it._data.settlerStatusId);
            it._controls.grid.sizeColumnsToFit();
        }

        function onSelectionChanged(event) {
            it.copyData();
            it.toggleButtons(it._data.settlerStatusId);
        }
    },

    getGridData: function () {
        const it = this;

        return {
            RecyclingOrderId: it._data.RecyclingOrderId,
            HideZeroPriceServices: $(it._sel.ckHideZeroPriceServices).is(':checked')
        };
    },

    saveData: function (eventParams) {
        const it = this;

        if (!it.canEdit()) {
            return;
        }

        const rowData = {
            RecyclingOrderId: it._data.RecyclingOrderId,
            ServiceId: eventParams.data.ServiceItemId,
            Qty: eventParams.data.Qty,
            Price: eventParams.data.Price,
            PriceTypeId: eventParams.data.PriceTypeId,
            Descr: eventParams.data.Descr,
            Notes: eventParams.data.Notes
        };

        const originalNode = eventParams.node;
        const masterGridApi = it._controls.grid.gridOptions.api;

        masterGridApi.showLoadingOverlay();

        Url.PostAsync(it._urls.save, { service: rowData }, function (saveResponse) {
            ProcessAjaxItemResponse(saveResponse, function (saveResponse) {
                if (originalNode) {
                    if (saveResponse.Item2) {//warningMsg
                        NoticeAlert.showWarning(saveResponse.Item2);
                    }
                    it.refreshMasterRow(masterGridApi, originalNode, eventParams.data.ServiceItemId);
                }
            }, function (msg) {
                masterGridApi.hideOverlay();
                NoticeAlert.showError('Attention', msg || "Failed to update detail");
            });
        }, null, function () {
            masterGridApi.hideOverlay();
        });
    },

    beforeSelect: function (params) {
        const it = this;

        const isEnabled = it.canEdit();

        const newValue = params.newValue;
        if (isEnabled) {
            if (params.colDef.field === "Descr") {
                params.data.Descr = newValue;
            }
            else if (params.colDef.field === "Qty") {
                const qty = Number.parse(newValue, -1);
                if (qty >= 0) {
                    params.data.Qty = qty;
                } else {
                    return false;
                }
            }
            else if (params.colDef.field === "Price") {
                const price = Number.parse(newValue, 0);
                if (Number.isNumber(newValue)) {
                    params.data.Price = price;
                } else {
                    return false;
                }
            }
            else if (params.colDef.field === "Notes") {
                params.data.Notes = newValue;
            }

            return true;
        } else {

            return false;
        }
    },

    copyData: function () {
        const it = this;

        const row = it.getSelectedRow();
        if (row) {
            // fill the inputs
            it._controls.acServices.SetData({
                label: row.ServiceTypeCd,
                value: row.ServiceTypeId
            });
            $(it._sel.spQty).val(row.Qty);
            $(it._sel.spPrice).val(row.Price.toFixed(it._options.priceDecimals));
            $(it._sel.ddPriceType).val(row.PriceTypeId);
            $(it._sel.taNotes).val(row.Notes);
        }

        const isEditable = it.canEdit();
        it._controls.menu.ToggleEnabled({
            Add: isEditable,
            Delete: isEditable && Object.exists(row)
        });
    },

    canEdit: function () {
        const it = this;

        // return true if settlement is not completed
        return it._isOutbound && it._data.settlerStatusId != RecyclingOrderStatuses.Settled || !it._isOutbound && it._data.settlerStatusId != InboundOrderStatuses.SettlementComplete;
    },

    getSelectedRow: function () {
        const it = this;
        return it._controls.grid ? _.first(it._controls.grid.getSelectedRows()) : null;
    },

    ReloadGrid: function () {
        const r = this;
        if (r._controls.grid) {
            r._controls.grid.reload();
        }
    },

    reloadCurrency: function () {
        const it = this;

        $(it._sel.spanPrice).LabelPayCharge({ pay: 'Pay:', charge: 'Charge:', hideLabelIfZero: true, precesion: 2, mark: RecyclingOrderContext.GetCurrencyMark() });
    },

    handleDetailCellChange: function (event) {
        const it = this;

        const masterGridApi = it._controls.grid.gridOptions.api;

        const parentNode = this.findParentNodeForDetail(event.node);
        if (!parentNode) {
            NoticeAlert.showError('Attention', "Parent node not found for detail row");
            return;
        }

        const parentData = parentNode.data;
        if (!parentData || !parentData.ServiceTypeId) {
            NoticeAlert.showError('Attention', "Parent data or ServiceTypeId not found");
            return;
        }

        const changedData = {
            id: event.data.Id,
            recyclingOrderId: it._data.RecyclingOrderId,
            serviceTypeId: event.data.ServiceTypeId,
            detailId: event.data.DetailId,
            estimatedQty: event.data.EstimatedQty,
            detailType: event.data.DetailType,
            newValue: event.newValue,
            oldValue: event.oldValue
        };

        event.api.showLoadingOverlay();

        Url.PostAsync(it._urls.updatDetail,
            {
                id: changedData.id,
                estimatedQty: changedData.estimatedQty,
                detailType: changedData.detailType,
                recyclingOrderId: changedData.recyclingOrderId,
                serviceTypeId: changedData.serviceTypeId,
                detailId: changedData.detailId
            }, function (response) {
                ProcessAjaxItemResponse(response, function () {
                    event.api.hideOverlay();
                    it.refreshMasterRow(masterGridApi, parentNode, parentData.ServiceTypeId);
                },
                    function (msg) {
                        event.api.applyTransaction({
                            update: [{
                                ...event.data,
                                [event.colDef.field]: event.oldValue
                            }]
                        });
                        event.api.hideOverlay();
                        NoticeAlert.showError('Attention', msg || "Failed to update detail");
                    });
            }, null, function () {
                event.api.hideOverlay();
            });
    },

    findParentNodeForDetail: function (detailNode) {
        const gridApi = this._controls.grid.gridOptions.api;
        let foundParent = null;

        gridApi.forEachNode((node) => {
            if (node.detailNode && node.data.ServiceTypeId == detailNode.data.ServiceTypeId) {
                foundParent = node;
            }
        });

        return foundParent;
    },

    refreshMasterRow: function (gridApi, parentNode, serviceItemId) {
        const it = this;

        Url.PostAsync(it._urls.loadMasterRow, {
            recyclingOrderId: it._data.RecyclingOrderId,
            serviceItemId: serviceItemId
        }, function (response) {
            if (response.data) {
                const updatedData = response.data;
                parentNode.setData(updatedData);

                setTimeout(() => {
                    it.calculateSubtotal(true);
                    gridApi.hideOverlay();
                }, 100);
            }
        });
    },

    filterPriceTypeOptions: function () {
        const it = this;
        const selectedServiceTypeId = it._controls.acServices.GetDataId();
        const ddPriceType = $(it._sel.ddPriceType);

        if (it.isServiceWithApplyType6(selectedServiceTypeId)) {
            const filteredPriceTypes = _.filter(it._priceTypes, function(priceType) {
                return priceType.value != 1;
            });
            ddPriceType.rebuildSelectOptions(filteredPriceTypes);
            
            if (ddPriceType.val() == 1) {
                ddPriceType.val(filteredPriceTypes[0].value);
            }
        } else {
            ddPriceType.rebuildSelectOptions(it._priceTypes);
        }
    },

    isServiceWithApplyType6: function (serviceTypeId) {
        const it = this;
        
        const service = _.find(it._data.servicesList, { value: serviceTypeId });
        
        if (service && service.ApplyTypeId == 6) {
            return true;
        }
        
        const acData = it._controls.acServices.GetData();
        
        if (acData && acData.ApplyTypeId == 6) {
            return true;
        }
        
        return false;
    }
};
