using Razor.Common.Web.JqGrid.FilterParser.FilterColumnValueAdjustment;
using Razor.Common.Web.JqGrid.FilterParser.Parser.Base;
using Razor.Common.Web.JqGrid.FilterParser.Parser.Components.OperationDictionaries;
using Razor.Tools.DataAccess.DataServiceBase.Convertable;

namespace Razor.Common.Web.JqGrid.FilterParser.Parser
{
    public class TextFilterStringBuilder : FilterStringBuilderBase
    {
        protected override IComparisonOperationsDictionary ComparisonOperations { get; } = new TextComparisonOperationsDictionary();
        public TextFilterStringBuilder(IFilterColumnValueAdjustmentService adjustService, IConvertiblesService convertiblesService) : base(adjustService, convertiblesService)
        {
        }
    }
}