<%@ Page Language="C#" 
    AutoEventWireup="true" 
    MasterPageFile="~/Site.Master" 
    CodeBehind="Packaging.aspx.cs" 
    Inherits="RazorWeb.Admin.SystemManagement.Packaging" 
    %>
<%@ Import Namespace="System.Web.Optimization" %>

<asp:Content ID="Content1" ContentPlaceHolderID="HeadContent" runat="server">
    
    <%-- JS --%>
    <%: Scripts.Render("~/bundles/Packaging.js") %>

    <script type="text/javascript">
        $(document).ready(function () {
            var terms = new GridPackaging();
        });
    </script>   
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="BehindTheMenuContent" runat="server">
</asp:Content>

<asp:Content ID="Content3" ContentPlaceHolderID="PageContent" runat="server">
      <div class="content-wrap clearfix">

        <%-- HEADER BUTTONS --%>
        <div class="buttons-block clearfix" id="div_WarehousesMenuButtons">
            <button id="bt_NewPackaging"     class="button btnSelectClass disabled">New</button>
            <button id="bt_EditPackaging"    class="button btnSelectClass disabled">Edit</button>                
            <button id="bt_DeletePackaging"  class="button btnSelectClass disabled">Delete</button>
        </div>
        
        <%-- CONTENT --%>
        <div class="content-wrapper content">            
            <div class="content-title">
                <h2 class="">Packaging</h2>
            </div>

            <table data-search-control="searchBar" id="jqg_Packaging"></table>
            <div id="jqp_Packaging"></div>
        </div>

    </div>
</asp:Content>

<asp:Content ID="Content4" ContentPlaceHolderID="Dialogs" runat="server">
       <div id="edit_packaging_holder" class="loc-popap clearfix" style="display: none">        
        <div class="loc-popap-item clearfix">
            <div class="loc-popap-item-left">
                <label for="tb_PackagingName" class="short-label">Packaging Name</label>
            </div>       
            <div class="loc-popap-item-right">
                <input  id="tb_PackagingName" type="text"/>
            </div>
        </div>     
        <div class="loc-popap-item clearfix">
            <div class="loc-popap-item-left">
                <label for="tb_PackagingWeight" class="short-label">Packaging Weight</label>
            </div>       
            <div class="loc-popap-item-right clearfix">
                <div style="width: 74px; float: left;">
                    <input  id="tb_PackagingWeight" type="text"/>
                </div>
                <span style="margin: 1px 0 0 10px; display: block; float: left;" class="weight-measure-unit-label"></span>
            </div>
        </div>      
    </div> 
</asp:Content>
