//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace RazorWeb.Control.DialogInventoryItemAddRemoveParts {
    
    
    public partial class DialogInventoryItemAddRemoveParts {
        
        /// <summary>
        /// InventoryItemAddRemovePartsInfo1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::RazorWeb.Control.DialogInventoryItemAddRemoveParts.InventoryItemAddRemovePartsInfo InventoryItemAddRemovePartsInfo1;
        
        /// <summary>
        /// ucTabInventoryItemAddParts control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::RazorWeb.Control.DialogInventoryItemAddRemoveParts.TabInventoryItemAddParts ucTabInventoryItemAddParts;
        
        /// <summary>
        /// ucTabInventoryItemRemoveParts control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::RazorWeb.Control.DialogInventoryItemAddRemoveParts.TabInventoryItemRemoveParts ucTabInventoryItemRemoveParts;
    }
}
