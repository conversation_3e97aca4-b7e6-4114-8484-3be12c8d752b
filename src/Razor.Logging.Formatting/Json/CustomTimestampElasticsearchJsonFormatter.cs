using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using Serilog.Events;
using Serilog.Formatting.Elasticsearch;
using Serilog.Parsing;

namespace Razor.Logging.Formatting.Json
{
    public class CustomTimestampElasticsearchJsonFormatter : CustomTimestampJsonTextFormatter
    {
        /// <summary>Render message property name</summary>
        public const string RenderedMessagePropertyName = "message";

        /// <summary>Message template property name</summary>
        public const string MessageTemplatePropertyName = "messageTemplate";

        /// <summary>Exception property name</summary>
        public const string ExceptionPropertyName = "Exception";

        /// <summary>Level property name</summary>
        public const string LevelPropertyName = "level";

        /// <summary>Timestamp property name</summary>
        public const string TimestampPropertyName = "@timestamp";

        private readonly bool _formatStackTraceAsArray;
        private readonly bool _inlineFields;
        private readonly ISerializer _serializer;
        private readonly IReadOnlyCollection<string> _timestampPropertyNames;

        /// <summary>
        ///     Construct a <see cref="T:Razor.Logging.Formatting.Json.CustomTimestampElasticsearchJsonFormatter" />.
        /// </summary>
        /// <param name="timestampPropertyNames">Timestamp property names, will be used to overrider @timestamp field</param>
        /// <param name="omitEnclosingObject">
        ///     If true, the properties of the event will be written to
        ///     the output without enclosing braces. Otherwise, if false, each event will be written as a well-formed
        ///     JSON object.
        /// </param>
        /// <param name="closingDelimiter">
        ///     A string that will be written after each log event is formatted.
        ///     If null, <see cref="P:System.Environment.NewLine" /> will be used. Ignored if
        ///     <paramref name="omitEnclosingObject" />
        ///     is true.
        /// </param>
        /// <param name="renderMessage">
        ///     If true, the message will be rendered and written to the output as a
        ///     property named RenderedMessage.
        /// </param>
        /// <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
        /// <param name="serializer">Inject a serializer to force objects to be serialized over being ToString()</param>
        /// <param name="inlineFields">When set to true values will be written at the root of the json document</param>
        /// <param name="renderMessageTemplate">
        ///     If true, the message template will be rendered and written to the output as a
        ///     property named RenderedMessageTemplate.
        /// </param>
        /// <param name="formatStackTraceAsArray">If true, splits the StackTrace by new line and writes it as a an array of strings</param>
        public CustomTimestampElasticsearchJsonFormatter(IReadOnlyCollection<string> timestampPropertyNames,
            bool omitEnclosingObject = false,
            string closingDelimiter = null,
            bool renderMessage = true,
            IFormatProvider formatProvider = null,
            ISerializer serializer = null,
            bool inlineFields = false,
            bool renderMessageTemplate = true,
            bool formatStackTraceAsArray = false)
            : base(omitEnclosingObject, closingDelimiter, renderMessage, formatProvider, renderMessageTemplate)
        {
            _timestampPropertyNames = timestampPropertyNames;
            _serializer = serializer;
            _inlineFields = inlineFields;
            _formatStackTraceAsArray = formatStackTraceAsArray;
        }

        /// <summary>Writes out individual renderings of attached properties</summary>
        protected override void WriteRenderings(
            IGrouping<string, PropertyToken>[] tokensWithFormat,
            IReadOnlyDictionary<string, LogEventPropertyValue> properties,
            TextWriter output)
        {
            output.Write(",\"{0}\":{{", "renderings");
            WriteRenderingsValues(tokensWithFormat, properties, output);
            output.Write("}");
        }

        /// <summary>Writes out the attached properties</summary>
        protected override void WriteProperties(
            IReadOnlyDictionary<string, LogEventPropertyValue> properties,
            TextWriter output)
        {
            if (!_inlineFields)
                output.Write(",\"{0}\":{{", "fields");
            else
                output.Write(",");
            WritePropertiesValues(properties, output);
            if (_inlineFields)
                return;
            output.Write("}");
        }

        /// <summary>Writes out the attached exception</summary>
        protected override void WriteException(
            Exception exception,
            ref string delim,
            TextWriter output)
        {
            output.Write(delim);
            output.Write("\"");
            output.Write("exceptions");
            output.Write("\":[");
            delim = "";
            WriteExceptionSerializationInfo(exception, ref delim, output, 0);
            output.Write("]");
        }

        private void WriteExceptionSerializationInfo(
            Exception exception,
            ref string delim,
            TextWriter output,
            int depth)
        {
            while (true)
            {
                output.Write(delim);
                output.Write("{");
                delim = "";
                WriteSingleException(exception, ref delim, output, depth);
                output.Write("}");
                delim = ",";
                if (exception.InnerException != null && depth < 20)
                {
                    exception = exception.InnerException;
                    depth = ++depth;
                }
                else
                {
                    break;
                }
            }
        }

        /// <summary>
        ///     Writes the properties of a single exception, without inner exceptions
        ///     Callers are expected to open and close the json object themselves.
        /// </summary>
        /// <param name="exception"></param>
        /// <param name="delim"></param>
        /// <param name="output"></param>
        /// <param name="depth"></param>
        protected void WriteSingleException(
            Exception exception,
            ref string delim,
            TextWriter output,
            int depth)
        {
            var info = new SerializationInfo(exception.GetType(), new FormatterConverter());
            var context = new StreamingContext();
            exception.GetObjectData(info, context);
            var helpUrl = info.GetString("HelpURL");
            var stackTraceString = info.GetString("StackTraceString");
            var remoteStackTraceString = info.GetString("RemoteStackTraceString");
            var remoteStackIndex = info.GetInt32("RemoteStackIndex");
            var exceptionMethodString = info.GetString("ExceptionMethod");
            var hResult = info.GetInt32("HResult");
            var sourceString = info.GetString("Source");
            var classNameString = info.GetString("ClassName");
            WriteJsonProperty("Depth", depth, ref delim, output);
            WriteJsonProperty("ClassName", classNameString, ref delim, output);
            WriteJsonProperty("Message", exception.Message, ref delim, output);
            WriteJsonProperty("Source", sourceString, ref delim, output);
            if (_formatStackTraceAsArray)
            {
                WriteMultilineString("StackTrace", stackTraceString, ref delim, output);
                WriteMultilineString("RemoteStackTrace", stackTraceString, ref delim, output);
            }
            else
            {
                WriteJsonProperty("StackTraceString", stackTraceString, ref delim, output);
                WriteJsonProperty("RemoteStackTraceString", remoteStackTraceString, ref delim, output);
            }

            WriteJsonProperty("RemoteStackIndex", remoteStackIndex, ref delim, output);
            WriteStructuredExceptionMethod(exceptionMethodString, ref delim, output);
            WriteJsonProperty("HResult", hResult, ref delim, output);
            WriteJsonProperty("HelpURL", helpUrl, ref delim, output);
        }

        private void WriteMultilineString(
            string name,
            string value,
            ref string delimiter,
            TextWriter output)
        {
            string[] strArray;
            if (value == null)
                strArray = null;
            else
                strArray = value.Split(new[]
                {
                    Environment.NewLine
                }, StringSplitOptions.RemoveEmptyEntries);
            if (strArray == null)
                strArray = Array.Empty<string>();
            var sequence = strArray;
            WriteJsonArrayProperty(name, sequence, ref delimiter, output);
        }

        private void WriteStructuredExceptionMethod(
            string exceptionMethodString,
            ref string delim,
            TextWriter output)
        {
            if (string.IsNullOrWhiteSpace(exceptionMethodString))
                return;
            var strArray = exceptionMethodString.Split(char.MinValue, '\n');
            if (strArray.Length != 5)
                return;
            var num = int.Parse(strArray[0], CultureInfo.InvariantCulture);
            var str1 = strArray[1];
            var assemblyName1 = strArray[2];
            var str2 = strArray[3];
            var str3 = strArray[4];
            var assemblyName2 = new AssemblyName(assemblyName1);
            output.Write(delim);
            output.Write("\"");
            output.Write("exceptionMethod");
            output.Write("\":{");
            delim = "";
            WriteJsonProperty("Name", str1, ref delim, output);
            WriteJsonProperty("AssemblyName", assemblyName2.Name, ref delim, output);
            WriteJsonProperty("AssemblyVersion", assemblyName2.Version.ToString(), ref delim, output);
            WriteJsonProperty("AssemblyCulture", assemblyName2.CultureName, ref delim, output);
            WriteJsonProperty("ClassName", str2, ref delim, output);
            WriteJsonProperty("Signature", str3, ref delim, output);
            WriteJsonProperty("MemberType", num, ref delim, output);
            output.Write("}");
            delim = ",";
        }

        /// <summary>(Optionally) writes out the rendered message</summary>
        protected override void WriteRenderedMessage(
            string message,
            ref string delim,
            TextWriter output)
        {
            WriteJsonProperty(nameof(message), message, ref delim, output);
        }

        /// <summary>Writes out the message template for the log event.</summary>
        protected override void WriteMessageTemplate(
            string template,
            ref string delim,
            TextWriter output)
        {
            WriteJsonProperty("messageTemplate", template, ref delim, output);
        }

        /// <summary>Writes out the log level</summary>
        protected override void WriteLevel(LogEventLevel level, ref string delim, TextWriter output)
        {
            WriteJsonProperty(nameof(level), Enum.GetName(typeof(LogEventLevel), level), ref delim, output);
        }

        /// <summary>Writes out the log timestamp</summary>
        protected override void WriteTimestamp(
            DateTimeOffset timestamp,
            ref string delim,
            TextWriter output)
        {
            WriteJsonProperty("@timestamp", timestamp, ref delim, output);
        }

        /// <summary>
        ///     Allows a subclass to write out objects that have no configured literal writer.
        /// </summary>
        /// <param name="value">The value to be written as a json construct</param>
        /// <param name="output">The writer to write on</param>
        protected override void WriteLiteralValue(object value, TextWriter output)
        {
            if (_serializer != null)
            {
                var str = _serializer.SerializeToString(value);
                output.Write(str);
            }
            else
            {
                base.WriteLiteralValue(value, output);
            }
        }

        protected override DateTimeOffset GetTimeStampValue(LogEvent logEvent)
        {
            try
            {
                foreach (var propertyName in _timestampPropertyNames)
                {
                    if (!logEvent.Properties.TryGetValue("RazorLogEvent", out var record)) continue;
                    if (!(record is StructureValue structureValue)) continue;
                    
                    var propertyValue = structureValue.Properties.FirstOrDefault(p =>
                            p.Name.Equals(propertyName, StringComparison.InvariantCultureIgnoreCase))
                        ?.Value;
                    if (propertyValue is ScalarValue scalarValue &&
                        scalarValue.Value is DateTime timestampValue)
                    {
                        return new DateTimeOffset(timestampValue);
                    }
                }
            }
            catch
            {
                return logEvent.Timestamp;
            }

            return logEvent.Timestamp;
        }
    }
}