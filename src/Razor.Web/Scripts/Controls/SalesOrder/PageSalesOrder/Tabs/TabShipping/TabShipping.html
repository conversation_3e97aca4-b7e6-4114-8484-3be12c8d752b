<div class="shipping">

    <div class="shipping-address-det clearfix">

        <h4 class="line-bottom">Shipments</h4>
        <div class="shipmentDetailsWr clearfix" id="divGridShippings">

        </div>

        <!--SHIPPING ADDRESS DETAILS-->
        <div class="shipmentDetailsWr __ship-destination clearfix">
            <div class="line-bottom">
                <h4 style="display: inline-block">Shipping Address Details</h4>
                <a id="bt_InternationalShipOptions" class="btnSelectClass" style="display: none !important; color: #fff !important; background-color: #82b555; text-shadow: none; padding: 3px 18px; font-weight: normal; margin-right: -2px; display: inline-block; background-image: none; border-radius: 0;">Customs Declaration</a>
                <div style="display: none" id="div_IntenationalShippingOptions"></div>
            </div>
            <!--Ship FROM-->
            <div class="shipmentDetailsItem __with-wide-fields">
                <div class="shipmentDetailsItemLine sameHeight clearfix">

                    <div class="shipDetItemLineLeft" style="width: 260px; margin-bottom: 10px;">
                        <label for="dd_From" class="__bold">Ship From:</label>
                        <a id="bt_ModifyFromAddress" class="btnSelectClass pencil"></a>
                        <a id="bt_RestoreFrom" class="btnSelectClass miscLuVal left" href="#" style="float: right; padding: 1px 7px 1px 7px;">
                            <i class="fa fa-undo" aria-hidden="true"></i>
                        </a>
                    </div>

                    <div class="shipDetItemLineRight doubleInput">
                        <input type="text" id="dd_From" />
                    </div>

                </div>

                <div class="shipmentDetailsItemLine __with-wide-fields clearfix">
                    <div class="shipDetItemLineRight required">
                        <textarea id="ta_FromAddress" readonly="readonly"></textarea>
                    </div>
                </div>

            </div>

            <!--Ship TO-->
            <div class="shipmentDetailsItem">
                <div class="shipmentDetailsItemLine __with-wide-fields sameHeight clearfix">
                    <div class="shipDetItemLineLeft" style="width: 260px; margin-bottom: 10px;">
                        <label for="dd_To" class="__bold">Ship To:</label>
                        <a id="bt_ModifyToAddress" class="btnSelectClass pencil"></a>
                        <a id="bt_RestoreTo" class="btnSelectClass miscLuVal left" href="#" style="float: right; padding: 1px 7px 1px 7px;">
                            <i class="fa fa-undo" aria-hidden="true"></i>
                        </a>
                    </div>
                    <div class="shipDetItemLineRight clearfix">
                        <div class="clearfix">
                            <input type="text" id="dd_To" />
                        </div>
                    </div>
                </div>

                <div class="shipmentDetailsItemLine __with-wide-fields clearfix">
                    <div class="shipDetItemLineRight">
                        <input id="dd_ToAddresses" type="text" class="cqo-input-text" style="float: left; margin: 0 0 5px 0;" />
                        <div class="required">
                            <textarea id="ta_ToAddress" readonly="readonly"></textarea>
                        </div>
                        <textarea id="ta_BillToAddress" readonly="readonly" style="display: none;"></textarea>
                    </div>
                </div>

                <div class="shipmentDetailsItemLine  clearfix">
                    <div class="shipDetItemLineRight doubleInput">
                        <div class="__with-wide-fields">
                            <input id="dd_ToContact" type="text" class="cqo-input-text" style="float: left; margin: 0 0 5px 0;" />
                        </div>
                    </div>

                    <div class="shipDetItemLineRight">
                        <div class="clearfix" style="margin: 4px 0 0 0;">
                            <input type="checkbox" id="cb_Residential" />
                            <label class="__bold" for="cb_Residential">Residential delivery</label>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>

        <div class="shipment-metod-wr clearfix">
            <!--SHIPPING METHOD AND PROVIDER-->
            <div class="line-bottom">
                <h4 style="display: inline-block;">Shipping Method</h4>
            </div>

            <div class="shipment-metod">

                <div class="shipmentDetailsWr clearfix">
                    <div class="shipmentDetailsItem">
                        <div class="shipmentDetailsItemLine clearfix">
                            <div class="shipDetItemLineLeft">
                                <label for="ddShipBy" class="">Ship by:</label>
                            </div>
                            <div class="shipDetItemLineRight">
                                <select id="ddShipBy"></select>
                            </div>
                        </div>
                    </div>
                    <div class="shipmentDetailsItem carrier-settings">
                        <div class="shipmentDetailsItemLine clearfix">
                            <div class="shipDetItemLineLeft">
                                <label for="ddProvider" class="">Carrier:</label>
                            </div>
                            <div class="shipDetItemLineRight">
                                <select id="ddProvider"></select>
                            </div>
                        </div>
                    </div>

                    <div class="carrier-settings"> <!--Carrier settings-->
                        <div id="div_CarrierSettings" class="shipmentDetailsItem">
                            <div class="shipmentDetailsItemLine clearfix">
                                <div class="shipDetItemLineLeft">
                                    <label for="ddMethod" class="">Service class:</label>
                                </div>
                                <div class="shipDetItemLineRight required">
                                    <input type="text" id="ddMethod" />
                                </div>
                            </div>

                            <div class="shipmentDetailsItemLine clearfix">
                                <div class="shipDetItemLineLeft">
                                    <label for="tbReferenceId" class="">Reference #:</label>
                                </div>
                                <div class="shipDetItemLineRight">
                                    <input type="text" id="tbReferenceId" class="" maxlength="43" />
                                </div>
                            </div>
                            <div class="shipmentDetailsItemLine clearfix">
                                <div class="shipDetItemLineLeft">
                                    <label for="cbIsDirectDelivery" class="">Is Direct Delivery:</label>
                                </div>
                                <div class="shipDetItemLineRight">
                                    <input type="checkbox" id="cbIsDirectDelivery" />
                                </div>
                            </div>
                            <div class="shipmentDetailsItemLine clearfix">
                                <div class="shipDetItemLineLeft">
                                    <label for="ddShipmentSignatureType" class="">Signature Type:</label>
                                </div>
                                <div class="shipDetItemLineRight">
                                    <select id="ddShipmentSignatureType">
                                    </select>
                                </div>
                            </div>
                            <div class="shipmentDetailsItem">
                                <div class="shipmentDetailsItemLine clearfix">
                                    <div class="shipDetItemLineLeft">
                                        <label for="ddPostageProvider" class="" style="display: none;">Postage provider:</label>
                                    </div>
                                    <div class="shipDetItemLineRight">
                                        <select id="ddPostageProvider" style="display: none;">
                                            <option value="0">USPS</option>
                                            <option value="1">Endicia</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                        </div><!-- End: Carrier settings-->
                    </div>

                    <div class="shipmentDetailsItem freight-settings">
                        <div class="shipmentDetailsItemLine clearfix">
                            <div class="shipDetItemLineLeft">
                                <label for="tbCarrierCustomers">Freight Carrier:</label>
                            </div>
                            <div class="shipDetItemLineRight">
                                <input id="tbCarrierCustomers" type="text" />
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="available-for-selected carrier-settings">
                <div id="div_BlockDetails" class="ui-widget-overlay" style="display: none;">
                    <span>Unavailable for the selected "Ship by" value</span>
                </div>

                <!--SHIPMENT DETAILS-->
                <div class="shipmentDetailsWr clearfix __bill-to">

                    <div id="div_EmailTypeSettings" class="shipmentDetailsItemLine clearfix">
                        <div class="shipDetItemLineLeft">
                            <label for="cb_EmailType" class="">Email type:</label>
                        </div>
                        <div class="shipDetItemLineRight">
                            <select id="cb_EmailType">
                                <option value="0">Input Email</option>
                                <option value="1">Main Email</option>
                                <option value="2">CC Email</option>
                            </select>
                        </div>
                    </div>

                    <!--PAYORS CONTROL-->
                    <div id="shipmentDutiesBillTo" class="shipmentDetailsItem">
                        <div class="shipmentDetailsItemLine clearfix">
                            <div class="shipDetItemLineLeft">
                                <label for="ddDutiesPayor">Bill to (for duties):</label>
                            </div>
                            <div class="shipDetItemLineRight doubleInput">
                                <select id="ddDutiesPayor"></select>
                            </div>
                        </div>
                    </div>
                    <div class="shipmentDetailsItem">
                        <div class="shipmentDetailsItemLine clearfix">
                            <div class="shipDetItemLineLeft">
                                <label for="ddPayor">Bill to:</label>
                            </div>
                            <div class="shipDetItemLineRight doubleInput">
                                <select id="ddPayor"></select>
                                <a id="aEditPayor" class="btnSelectClass pencil" href="#" target="_blank" style="float: right; padding: 5px 7px;"></a>
                            </div>
                        </div>
                    </div>
                    <div class="shipmentDetailsItem  __bill-to">

                        <div class="shipmentDetailsItemLine clearfix" style="display: none;">
                            <div class="shipDetItemLineLeft">
                                <label for="tbPayorAccNo">Account #:</label>
                            </div>
                            <div class="shipDetItemLineRight doubleInput required">
                                <input style="clear: both; float: left;" type="text" id="tbPayorAccNo" />
                                <a id="aReloadAccounts" alt="R" class="btnSelectClass fa fa-undo" href="#" style="float: right; padding: 5px 6px;"></a>
                            </div>
                        </div>

                        <div class="shipmentDetailsItemLine clearfix" style="display: none;">
                            <div class="shipDetItemLineLeft">
                                <label for="tbPayorName">Name:</label>
                            </div>
                            <div class="shipDetItemLineRight required">
                                <input type="text" id="tbPayorName" />
                            </div>
                        </div>

                        <div class="shipmentDetailsItem lastDetItem">

                            <div class="shipmentDetailsItemLine clearfix" style="display: none;">
                                <div class="shipDetItemLineLeft">
                                    <label for="tbPayorCountry">Coutry code:</label>
                                </div>
                                <div class="shipDetItemLineRight required">
                                    <input type="text" id="tbPayorCountry" />
                                </div>
                            </div>

                            <div class="shipmentDetailsItemLine clearfix" style="display: none;">
                                <div class="shipDetItemLineLeft required">
                                    <label for="tbPayorPostalCode">Postal code:</label>
                                </div>
                                <div class="shipDetItemLineRight required">
                                    <input type="text" id="tbPayorPostalCode" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="div_ShippingEstimateSettings" class="shipmentDetailsItem">

                        <div class="shipmentDetailsItemLine clearfix">
                            <div class="shipDetItemLineLeft">
                                <label for="laEstimate" class="">Shipping estimate:</label>
                            </div>
                            <div class="shipDetItemLineRight doubleInputWideBTN">
                                <span id="laEstimate"></span>
                                <div style="display: none" id="div_RateCalculator"></div>
                            </div>
                        </div>

                        <div class="shipmentDetailsItemLine clearfix">
                            <a id="bt_RateCalculator" class="btnSelectClass" style="color: #fff !important; background-color: #82b555; text-shadow: none; padding: 3px 5px; font-weight: normal; margin: 0; display: inline-block; background-image: none; border-radius: 0; box-sizing: border-box; padding: 3px 18px; text-align: center;">Get Rates</a>
                            <a id="bt_EstimateShipping" class="btnSelectClass" style="color: #fff !important; background-color: #82b555; text-shadow: none; padding: 3px 5px; font-weight: normal; display: inline-block; background-image: none; border-radius: 0; margin: 0; box-sizing: border-box; padding: 3px 18px; text-align: center; margin-right: 3px;">Estimate</a>
                        </div>
                    </div>
                </div>
            </div>

            <div id="div_TrackingEmailSettings" class="shipmentDetailsItemLine clearfix mod-notify">
                <div class="shipDetItemLineLeft shiplabelCheckbox clearfix">
                    <label for="cbEnableTracking" class="">Email:</label>
                    <input type="checkbox" id="cbEnableTracking" checked="checked" />

                </div>
                <div class="shipDetItemLineRight shiplabelCheckbox clearfix">
                    <input type="text" id="tbTrackToEmail" class="" />
                </div>
            </div>
        </div>

        <!--PACKAGE CONTROL-->
        <h4 class="line-bottom package-title">Package Details <span class="additional-title">Tracking Information</span></h4>

        <div class="clearfix packages-details-block" id="div_ShippingPackages"></div>

        <!-- FOOTER BUTTONS -->
        <div class="full-width sales-order-btns clearfix" id="div_ShippingFooterButtons" style="text-align: right; margin: 10px 0 0 0; padding: 5px 5px;">
            <a id="bt_Save" class="button btnSelectClass" href="#">Save</a>
            <a id="bt_Clear" class="button btnSelectClass" href="#">Clear</a>
            <a id="bt_VoidShipment" class="button btnSelectClass" href="#">Void</a>
            <a id="bt_ApproveShipment" class="button btnSelectClass" href="#">Approve</a>
            <a id="bt_Ship" class="button btnSelectClass" href="#">Ship</a>
        </div>

        <!-- INVISIBLE CONTAINER FOR ADDRESS EDITOR DIALOG POPUP -->
        <div id="div_AddressEditor" style="display: none;"></div>
        <div id="div_DialogCancelShipping" style="display: none;"></div>

    </div>

</div>
<style type="text/css">
    #cb_EmailType {
        width: 180px;
    }

     #packages-header-menu span.ui-spinner.ui-widget.ui-widget-content.ui-corner-all {
         width: 40px;
     }

    #packages-header-menu-dropdowns {
        float: left;
    }

    #div_PackageMenuButtons {
        float: left;
    }

    .jqgPacckaging-table .ui-jqgrid-bdiv {
        height: 213px !important;
        overflow-y: scroll;
    }

    .packagingType-table .ui-jqgrid-bdiv {
        overflow-y: scroll;
    }

    .shipmentDetailsItem {
        margin: 0 15px 0 0;
        width: 335px;
    }

    /*.shipDetItemLineLeft {
        width: 33%;
    }*/

    .shipDetItemLineRight {
        width: 210px;
    }

    .shipment-metod-wr {
        float: left;
        width: 50%;
        padding-left: 20px;
        box-sizing: border-box;
    }

    .shipment-metod-wr .shipment-metod {
        max-width: 50%;
        display: inline-block;
        vertical-align: top;
        float: none;
        padding-right: 20px;
        box-sizing: border-box;
    }

    .shipment-metod-wr .available-for-selected {
        max-width: 49%;
        display: inline-block;
        vertical-align: top;
        float: none;
        padding-left: 20px;
        box-sizing: border-box;
        margin: 0;
    }

    .shipment-metod {
        max-width: 653px;
        float: left;
    }

    .shipment-metod .shipmentDetailsItem {
        width: 316px;
        margin: 0 0 0 0;
    }

    .shipment-metod .shipmentDetailsItem:first-child {
        margin: 0;
    }

    /*.shipment-metod .shipDetItemLineLeft {
            width: 100px;
        }*/

        

    .shipment-metod .shipDetItemLineRight.doubleInputWideBTN input[type="text"] {
        width: 48%;
    }

    .available-for-selected {
        max-width: 650px;
        float: left;
        margin-left: 10px;
    }

    .available-for-selected .shipmentDetailsItem {
        width: 100%;
        margin: 0 0 0 0;
    }

    .available-for-selected .shipmentDetailsItem:first-child {
        margin: 0;
    }

    .available-for-selected .shipmentDetailsItem.lastDetItem {
        margin: 0;
    }

    /*.available-for-selected .shipDetItemLineLeft {
            width: 119px;
        }*/
    .shipment-metod .shipDetItemLineRight {
        width: 60%;
    }
    .available-for-selected .shipDetItemLineRight {
        width: 60%;
    }

    .shipDetItemLineRight.doubleInput input[type="text"] {
        width: 137px;
    }

    .available-for-selected .shipDetItemLineRight.__with-btn input[type="text"],
    .available-for-selected .shipDetItemLineRight.__with-btn label {
        width: 25%;
        display: inline-block;
    }

    .available-for-selected #div_BlockDetails {
        top: 0 !important;
        right: 0;
        width: auto;
    }
    .shipmentDetailsWr.__ship-destination {
        float: left;
        width: 50%;
        padding-right: 20px;
        box-sizing: border-box;
    }

    .shipmentDetailsWr button.button.btnSelectClass {
        margin-top: 8px;    
    }

    .shipmentDetailsWr button.button.btnSelectClass:first-child {
        margin-left: 0;
    }

    .shipmentDetailsWr.__ship-destination .shipmentDetailsItem {
        width: 260px;
    }

    .shipmentDetailsWr.__ship-destination .shipmentDetailsItem.__with-wide-fields {
        width: 310px;
    }

    .shipmentDetailsWr .shipmentDetailsItem label.__bold {
        font-weight: bold;
        font-size: 13px;
        display: inline-block;
        vertical-align: -8px;
    }

    .shipmentDetailsWr .shipmentDetailsItem label {
        font-size: 13px;
    }

    .__with-wide-fields input,
    .__with-wide-fields textarea {
        width: 255px !important;
    }

    .shipmentDetailsItem .btnSelectClass.pencil {
        float: right;
        margin-left: 6px !important;
        background: url('../Images/btn-search-go-attach.png') no-repeat scroll -110px -2px transparent !important;
        padding: 4px 7px 1px 7px !important;
    }

    .shipmentDetailsItemLine {
        padding: 3px 0;
    }

    .shipmentDetailsItemLine.mod-notify {
        margin-top: 10px;
        padding: 0;
        font-size: 12px;
    }

    .shipmentDetailsItemLine.mod-notify input[type="checkbox"] {
        margin-top: 2px;
    }

    .shipmentDetailsItemLine.mod-notify .token-input-list-facebook {
        width: 100%;
    }

    .shipmentDetailsItemLine.mod-notify .shipDetItemLineRight {
        width: calc(100% - 71px);
    }

    .__bill-to .shipmentDetailsItemLine label {
        text-transform: capitalize;
    }

    .ui-widget-content .shipment-label-container .btnSelectClass {
        margin-top: 0;
        margin-bottom: 0;
        position: relative;
    }

    .counter-label {
        color: white;
        background-color: #555555;
        font-size: 11px !important;
        line-height: 16px;
        text-shadow: none;
        text-align: center;
        position: absolute;
        top: -5px;
        right: 14px;
        width: 16px;
        height: 16px;
        margin-right: -23px;
        border-radius: 50%;
        border-style: solid;
        border-color: #555555;
        border-width: 2px;
    }
    .package-title {
        border-bottom: 1px solid #CCCCCC;
        margin: 0 0 10px 0;
        height: auto;
        display: inline-block;
        width: 100%;
    }

    .shipDetItemLineLeft.shiplabelCheckbox {
        width: auto;
        display: inline-block; 
        margin-right: 15px;
        margin-top: 8px;
    }

    .shipDetItemLineLeft.shiplabelCheckbox label {
        display: block;
        line-height: 15px;
        font-size: 14px;
    }

    .shipDetItemLineRight.doubleInputWideBTN input[type="text"] {
        width: 100%;
        box-sizing: border-box;
    }

    .packages-details-block .btnSelectClass {
        margin-top: 8px;
    }

    .packages-details-block .btnSelectClass:first-child {
        margin-left: 0;
    }

    .shipmentDetailsItem input[type="checkbox"] {
        margin-top: 12px;
    }

    .line-bottom {
        margin-bottom: 5px;
    }

    .line-bottom h4,
    h4.line-bottom {
        padding-bottom: 5px;
        margin-bottom: 0;
        position: relative;
    }

    .additional-title {
        position: absolute;
        left: 50%;
        margin-left: 20px;
    }
</style>