@using Razor.Data.Core.DataServices.RecyclingOrder.Models.Audit
@model RecyclingOrderAuditReportModel

@{ RecyclingOrderAuditReportModel m = Model;}

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1.0, width=device-width">
    <title>Certificate</title>
    <link rel="stylesheet" href="stylesheets/pdf-certificate.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans">

    <style>
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }

        html {
            position: relative;
            min-height: 100%;
        }

        body {
            margin: 0;
        }

        .pdf-certificate {
            box-sizing: border-box;
            width: 992px;
            margin: 0 auto;
            padding: 25px 35px 160px;
            font-family: Arial, sans-serif;
        }

        .pdf-certificate__head {
            width: 100%;
            padding-top: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #000;
        }

        .pdf-certificate__logo {
            display: block;
            max-width: 100%;
            height: auto;
        }

        .pdf-certificate__info {
            padding-top: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #000;
        }

        .pdf-certificate__content {
            padding-top: 15px;
            padding-bottom: 15px;
        }

        .pdf-certificate__title {
            margin-top: 7px;
            margin-bottom: 7px;
            text-align: center;
            font-weight: 700;
            font-size: 28px;
        }

        .pdf-certificate__text {
            margin-top: 10px;
            margin-bottom: 15px;
            font-size: 12px;
            text-align: justify;
        }

        .pdf-certificate__text--bold {
            font-weight: 700;
        }

        .pdf-certificate__block {
            margin-top: 30px;
        }

        .pdf-certificate__subtitle {
            margin-bottom: 7px;
            font-weight: 700;
            font-size: 13px;
        }

        .pdf-certificate__sign {
            bottom: 70px;
            position: absolute;
        }

        .pdf-certificate .info-column {
            float: left;
            display: table;
            width: 256px;
            margin-left: 77px;
        }

            .pdf-certificate .info-column:first-child {
                margin-left: 0;
            }

        .pdf-certificate .info-column__row {
            display: table-row;
        }

        .pdf-certificate .info-column__title,
        .pdf-certificate .info-column__value {
            display: table-cell;
            width: 50%;
            padding-top: 4px;
            padding-bottom: 4px;
            font-size: 13px;
        }

        .pdf-certificate .info-column__title {
            font-weight: 700;
        }

        .pdf-certificate .pdf-table {
            display: table;
            width: 100%;
            font-size: 13px;
        }

        .pdf-certificate .pdf-table--no-lines {
        }

            .pdf-certificate .pdf-table--no-lines .pdf-table__row-item {
                border-color: transparent;
            }

        .pdf-certificate .pdf-table__head,
        .pdf-certificate .pdf-table__row {
            display: table-row;
        }

        .pdf-certificate .pdf-table__head-item, .pdf-certificate .pdf-table__row-item {
            display: table-cell;
            padding: 4px;
        }

        .pdf-certificate .pdf-table__head {
            font-weight: 700;
        }

        .pdf-certificate .pdf-table__head-item {
            border-top: 2px solid #000;
            border-bottom: 2px solid #000;
        }

        .pdf-certificate .pdf-table__row {
        }

        .pdf-certificate .pdf-table__row-item {
            color: #787578;
            border-bottom: 1px solid #000;
        }

        .pdf-certificate .pdf-sign {
            display: inline-block;
        }

        .pdf-certificate .pdf-sign__img {
            display: block;
            max-width: 100%;
            height: auto;
        }

        .pdf-certificate .pdf-sign__info {
            margin-top: -5px;
            padding-top: 7px;
            padding-bottom: 7px;
            font-size: 12px;
            border-top: 1px solid #000;
        }

    </style>

</head>

<body>
    <div class="pdf-certificate">
        <div class="pdf-certificate__head"><img class="pdf-certificate__logo" src="@(m.Header.HeaderUrl)"></div>
        <div class="pdf-certificate__info clearfix">
            <div class="info-column">
                <div class="info-column__row">
                    <div class="info-column__title">Date of delivery</div>
                    <div class="info-column__value">@m.RecieveOrderDate</div>
                </div>
                <div class="info-column__row">
                    <div class="info-column__title">Order number</div>
                    <div class="info-column__value">@m.AutoName</div>
                </div>
                <div class="info-column__row">
                    <div class="info-column__title">Invoice number</div>
                    <div class="info-column__value">
                        @if (m.PoInvoices != null)
                        {
                            @m.PoInvoices
                        }

                        @if (m.SoInvoices != null)
                        {
                            @m.SoInvoices
                        }
                    </div>
                </div>
                <div class="info-column__row">
                    <div class="info-column__title">BOL number</div>
                    <div class="info-column__value">@m.BolNumber</div>
                </div>
                <div class="info-column__row">
                    <div class="info-column__title">PO number</div>
                    <div class="info-column__value">@m.PoNumber</div>
                </div>
                <div class="info-column__row">
                    <div class="info-column__title">Contract #</div>
                    <div class="info-column__value">@m.Contract</div>
                </div>
            </div>
            <div class="info-column">
                <div class="info-column__row">
                    <div class="info-column__title">Generator</div>
                    <div class="info-column__value">
                        @if (m.PickupLocation != null)
                        {
                            // the last line is the phone
                            for (Int32 i = 0; i < m.PickupLocation.Length - 1; i++)
                            {
                                @m.PickupLocation[i]
                            }
                        }
                    </div>
                </div>
            </div>
            <div class="info-column">
                <div class="info-column__row">
                    <div class="info-column__title">Facility</div>
                    <div class="info-column__value">
                        @if (m.ShipTo != null)
                        {
                        @*the 1s line is company name, the last is the phone*@
                            for (Int32 i = 1; i < m.ShipTo.Length - 1; i++)
                            {
                                @m.ShipTo[i]

                            }
                        }
                    </div>
                </div>
            </div>
        </div>
        <div class="pdf-certificate__content">
            <div class="pdf-certificate__title">m.CertificateTemplate.Title</div>
            <div class="pdf-certificate__block">
                <div class="pdf-certificate__text pdf-certificate__text--bold">
                    @m.CertificateTemplate.Text
                </div>
            </div>
            <div class="pdf-certificate__block">
                <div class="pdf-certificate__subtitle">Order Summary</div>
                <div class="pdf-certificate__text">
                    @m.AuditOrder.OrderSummary
                </div>
            </div>
            <div class="pdf-certificate__table">
                <div class="pdf-certificate__block">
                    <div class="pdf-certificate__subtitle">Summary</div>
                    <div class="pdf-table">
                        <div class="pdf-table__head"><span class="pdf-table__head-item">Category</span><span class="pdf-table__head-item">Count</span><span class="pdf-table__head-item">Weight</span></div>
                        <div class="pdf-table__row"><span class="pdf-table__row-item">hard drivers</span><span class="pdf-table__row-item">155</span><span class="pdf-table__row-item">1512</span></div>
                        <div class="pdf-table__row"><span class="pdf-table__row-item">Laptops</span><span class="pdf-table__row-item">23</span><span class="pdf-table__row-item">215</span></div>
                        <div class="pdf-table__row"><span class="pdf-table__row-item">hard drivers</span><span class="pdf-table__row-item">155</span><span class="pdf-table__row-item">1512</span></div>
                        <div class="pdf-table__row"><span class="pdf-table__row-item">Laptops</span><span class="pdf-table__row-item">23</span><span class="pdf-table__row-item">215          </span></div>
                    </div>
                </div>

                <div class="pdf-certificate__info clearfix" style="page-break-inside: avoid !important">
                    <div class="info-column">
                        <div class="info-column__row">
                            <div class="info-column__title">Date of delivery</div>
                            <div class="info-column__value">@m.RecieveOrderDate</div>
                        </div>
                        <div class="info-column__row">
                            <div class="info-column__title">Order number</div>
                            <div class="info-column__value">@m.AutoName</div>
                        </div>
                        <div class="info-column__row">
                            <div class="info-column__title">Invoice number</div>
                            <div class="info-column__value">
                                @if (m.PoInvoices != null)
                                {
                                    @m.PoInvoices
                                }

                                @if (m.SoInvoices != null)
                                {
                                    @m.SoInvoices
                                }
                            </div>
                        </div>
                        <div class="info-column__row">
                            <div class="info-column__title">BOL number</div>
                            <div class="info-column__value">@m.BolNumber</div>
                        </div>
                        <div class="info-column__row">
                            <div class="info-column__title">PO number</div>
                            <div class="info-column__value">@m.PoNumber</div>
                        </div>
                        <div class="info-column__row">
                            <div class="info-column__title">Contract #</div>
                            <div class="info-column__value">@m.Contract</div>
                        </div>
                    </div>
                    <div class="info-column">
                        <div class="info-column__row">
                            <div class="info-column__title">Generator</div>
                            <div class="info-column__value">
                                @if (m.PickupLocation != null)
                                {
                                    // the last line is the phone
                                    for (Int32 i = 0; i < m.PickupLocation.Length - 1; i++)
                                    {
                                        @m.PickupLocation[i]
                                    }
                                }
                            </div>
                        </div>
                    </div>
                    <div class="info-column">
                        <div class="info-column__row">
                            <div class="info-column__title">Facility</div>
                            <div class="info-column__value">
                                @if (m.ShipTo != null)
                                {
                                    @*the 1s line is company name, the last is the phone*@
                                    for (Int32 i = 1; i < m.ShipTo.Length - 1; i++)
                                    {
                                        @m.ShipTo[i]

                                    }
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <div class="pdf-certificate__block">                  
                    <div class="pdf-certificate__subtitle">Detailed Asset Report</div>
                    <div class="pdf-table pdf-table--no-lines">
                        <div class="pdf-table__head">
                            <span class="pdf-table__head-item">Serial</span><span class="pdf-table__head-item">UID</span><span class="pdf-table__head-item">Asset Tag</span><span class="pdf-table__head-item">MFG</span><span class="pdf-table__head-item">Model</span>
                            <span class="pdf-table__head-item">Category</span>
                        </div>
                        @for (Int32 i = 0; i < m.Items.Length - 1; i++)
                        {
                        <div class="pdf-table__row">                          

                            <span class="pdf-table__row-item">@m.Items[i].SerialNumber</span>
                            <span class="pdf-table__row-item">@m.Items[i].UniqueId</span>
                            <span class="pdf-table__row-item">@m.Items[i].AssetTag</span>
                            <span class="pdf-table__row-item">@m.Items[i].ManufacturerCd</span>
                            <span class="pdf-table__row-item">@m.Items[i].ItemNumber</span>
                            <span class="pdf-table__row-item">@m.Items[i].CategoryFriendlyName</span>

                        </div> 
                        }
                    </div>
                </div>
            </div>
        </div>
        @if (m.Signature != null)
        {
            <div class="pdf-certificate__sign pdf-sign">
                <img class="pdf-sign__img" src="data:image/png;base64,@m.Signature.SignatureBase64Image">
                <div class="pdf-sign__info">
                    <div class="pdf-sign__name">@m.Signature.PersonName</div>
                    <div class="pdf-sign__position">@m.Signature.PersonJobTitle</div>
                </div>
            </div>
        }
    </div>
</body>

</html>

