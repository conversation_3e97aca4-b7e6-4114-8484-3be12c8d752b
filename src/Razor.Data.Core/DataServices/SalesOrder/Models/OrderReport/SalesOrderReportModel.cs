using System;
using Razor.Common.Core.Currency.Converter;
using Razor.Data.Core.CommonDataModels;

namespace Razor.Data.Core.DataServices.SalesOrder.Models.OrderReport
{
    public class SalesOrderReportModel
    {
        public Int64 SalesOrderId { set; get; }
        public String AutoName { set; get; }
        public String PO { set; get; }
        public Int32? TimeFrameDays { set; get; }
        public String TimeFrameName { set; get; }
        public String Terms { set; get; }
        public DateTime OrderedDate { set; get; }
        public String UserName { get; set; }

        public Int64 ContactId { set; get; }
        public Int64 CustomerAddressId { set; get; }
        public Int64 ShipFromAddressId { set; get; }
        public Int64 ShipToAddressId { set; get; }

        public Contact CustomerContact { set; get; }
        public PostalAddress CustomerAddress { set; get; }
        public PostalAddress ShipFrom { set; get; }
        public PostalAddress ShipTo { set; get; }

        public SalesOrderReportItem[] Items { set; get; }
        [CurrencyConversion]
        public Decimal Subtotal { set; get; }
        [CurrencyConversion]
        public Decimal Shipping { set; get; }
        [CurrencyConversion]
        public Decimal MiscCharge { set; get; }
        [CurrencyConversion]
        public Decimal TotalPrice { set; get; }
        public String CurrencyMark { get; set; }
    }
}
