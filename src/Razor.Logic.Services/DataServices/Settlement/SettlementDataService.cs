using System;
using System.Collections.Generic;
using System.Linq;
using Razor.Common.Base.CommonModels.Pagination;
using Razor.Common.Base.Context;
using Razor.Common.Core.UserAccess.Data;
using Razor.Common.Core.UserAccess.Enums;
using Razor.Data.Core.CommonDataModels.AutocompleteItems;
using Razor.Data.Core.DataServices.Invoice;
using Razor.Data.Core.DataServices.Invoice.Model.Void;
using Razor.Data.Core.DataServices.Recycling.Models;
using Razor.Data.Core.DataServices.RecyclingOrder.Models;
using Razor.Data.Core.DataServices.RecyclingOrder.Models.Audit;
using Razor.Data.Core.DataServices.RecyclingOrder.Models.Settlement;
using Razor.Data.Core.DataServices.RecyclingOrder.Models.Settlement.Services;
using Razor.Data.Core.DataServices.Settlement;
using Razor.Data.LegacyTableAdapters.DataAdapters.DsRecyclingTableAdapters;
using Razor.Logic.Services.DataServices.Invoice;
using Razor.Tools.DataAccess.DataServiceBase.AbstractImpl;
using Razor.Tools.DataAccess.DataServiceBase.Convertable;
using Razor.Tools.DataAccess.Helpers;
using Razor.Tools.DataAccess.OperationBlockings;

namespace Razor.Logic.Services.DataServices.Settlement
{
    public class SettlementDataService : DataServiceBase, ISettlementDataService
    {
        public SettlementDataService(ConnectionStringsConfig connectionStrings, IContextVariablesProvider context, IOperationBlockingTester operationBlockingTester,
            IConvertiblesService convertiblesService, IUserAccessDataService userAccessDataService, IInvoiceDataService invoiceDataService)
            : base(connectionStrings, context, operationBlockingTester, convertiblesService)
        {
            _userAccessDataService = userAccessDataService;
            _invoiceDataService = invoiceDataService;
        }

        public RecyclingOrderAuditRow[] GetRecyclingOrderAudits(
            Int64 recyclingOrderId,
            Int32 pageIndex,
            Int32 itemsPerPage,
            String orderColumn,
            String orderDirection,
            String filterWhere,
            long? parentId,
            out Int64 totalRows)
        {
            var mainArgs = new
            {
                RECYCLING_ORDER_ID = recyclingOrderId,
                PAGE_INDEX = pageIndex,
                ITEMS_PER_PAGE = itemsPerPage,
                ORDER_COLUMN_NAME = orderColumn ?? "RecyclingOrderAuditedDate",
                ORDER_DIRECTION = orderDirection ?? "DESC",
                FILTER_WHERE = filterWhere,
                PARENT_ID = parentId
            };

            var rows = Exec<RecyclingOrderAuditRow>("sp_GET_RECYCLING_ORDER_AUDIT_ITEMS_BY_ORDER_ID", mainArgs, commandTimeoutS: 600);

            totalRows = rows.First().RecyclingOrderAuditItemId;

            return rows.Skip(1).ToArray();
        }

        public AssetGrading[] GetAssetsDevaluations(Int64[] assetIds)
        {
            var rows = Exec<AssetGrading>("sp_GetAssetsDevaluations", new
            {
                AssetIds = ToTableParameter(assetIds)
            });

            return rows.ToArray();
        }

        public AssetGrading[] GetAssetsAllDevaluations(long recyclingOrderId)
        {
            var rows = Exec<AssetGrading>("sp_GetAssetsAllDevaluations", new
            {
                RecyclingOrderId = recyclingOrderId
            });

            return rows.ToArray();
        }

        public PageOfRows<SettlementOrderItemGridItem> GetRecyclingOrderItemsToSettle(long recyclingOrderId, int settlementStateId, bool viewSummary,
            string sortColumn, string sortOrder, int itemsPerPage, int pageIndex) =>
            GetPage<SettlementOrderItemGridItem>("dbo.sp_GET_RECYCLING_ORDER_ITEMS_TO_SETTLE", new
            {
                RECYCLING_ORDER_ID = recyclingOrderId,
                SETTLEMENT_STATE_ID = settlementStateId,
                MODE_SUMMARY = viewSummary,
                ORDER_COLUMN_NAME = sortColumn,
                ORDER_DIRECTION = sortOrder,
                ITEMS_PER_PAGE = itemsPerPage,
                PAGE_INDEX = pageIndex
            }, pageIndex, row => row.RecyclingOrderItemId ?? 0);

        public PageOfRows<SettlementOrderItemGridItem> GetRecyclingOrderItemsToSettleInitialDetial(long recyclingOrderId, long? parentId, string sortColumn,
            string sortOrder, int itemsPerPage, int pageIndex) =>
            GetPage<SettlementOrderItemGridItem>("dbo.sp_GET_RECYCLING_ORDER_ITEMS_TO_SETTLE_INITIAL_DETAIL", new
            {
                RECYCLING_ORDER_ID = recyclingOrderId,
                RECYCLING_ORDER_ITEM_PARENT_ID = parentId,
                ORDER_COLUMN_NAME = sortColumn,
                ORDER_DIRECTION = sortOrder,
                ITEMS_PER_PAGE = itemsPerPage,
                PAGE_INDEX = pageIndex
            }, pageIndex, row => row.RecyclingOrderItemId ?? 0);

        public PageOfRows<SettlementOrderItemGridItem> GetRecyclingOrderItemsToSettleInitialDetialExport(long recyclingOrderId, string sortColumn,
            string sortOrder, int itemsPerPage, int pageIndex) =>
            GetPage<SettlementOrderItemGridItem>("dbo.sp_GET_RECYCLING_ORDER_ITEMS_TO_SETTLE_INITIAL_DETAIL_EXPORT", new
            {
                RECYCLING_ORDER_ID = recyclingOrderId,
                ORDER_COLUMN_NAME = sortColumn,
                ORDER_DIRECTION = sortOrder,
                ITEMS_PER_PAGE = itemsPerPage,
                PAGE_INDEX = pageIndex
            }, pageIndex, row => row.RecyclingOrderItemId ?? 0);

        public IEnumerable<SettlementOutboundItemGridItem> GetRecyclingOrderOutboundAggregateItemsToSettle(long recyclingOrderId, string sortColumn, string sortOrder,
            int itemsPerPage, int pageIndex, string filters, out int totalRecords)
        {
            totalRecords = 0;
            var total = 0;
            var page = UsingTableAdapter<sp_GET_RECYCLING_ORDER_OUTBOUND_AGGREGATE_ITEMS_TO_SETTLETableAdapter, IEnumerable<SettlementOutboundItemGridItem>>(ta =>
            {
                using (var dt = ta.GetData(recyclingOrderId, sortColumn, sortOrder, itemsPerPage, pageIndex, filters))
                {
                    total = dt.Rows.Count > 0 && dt.Rows[0].ItemArray.Length > 0
                        ? Convert.ToInt32(dt.Rows[0][1])
                        : 0;
                    return MapDataTable<SettlementOutboundItemGridItem>(dt, 1).Distinct();
                }
            });
            totalRecords = total;
            return page;
        }

        public IEnumerable<SettlementOutboundItemGridItem> GetRecyclingOutboundOrderItemsToSettle(long recyclingOrderId,
            string sortColumn, string sortOrder,
            int itemsPerPage, int pageIndex, string filters)
        {
            return Exec<SettlementOutboundItemGridItem>("sp_GET_RECYCLING_ORDER_OUTBOUND_ITEMS_TO_SETTLE",
                new
                {
                    RECYCLING_ORDER_ID = recyclingOrderId,
                    ORDER_COLUMN_NAME = sortColumn,
                    ORDER_DIRECTION = sortOrder,
                    ITEMS_PER_PAGE = itemsPerPage,
                    PAGE_INDEX = pageIndex,
                    FILTER_WHERE = filters
                });
        }

        public PageOfRows<SettlementGridItem> GetReyclingOrdersToSettle(IPageRequest request, Int64[] customerIds, DateTime? startDate, DateTime? endDate,
           Int32 statusIds, Int64[] warehouseIds, String filters)
        {
            var args = new
            {
                CUSTOMER_IDS = ToTableParameter(customerIds),
                START_DATE = startDate,
                END_DATE = endDate,
                ORDER_STATUS_ID = statusIds,
                REP_IDS = ToTableParameter(_userAccessDataService.GetUserRepFollowersForRestriction(EntityActions.RestrictedInbound)),
                WAREHOUSE_IDS = ToTableParameter(warehouseIds),
                ORDER_COLUMN_NAME = request.sidx,
                ORDER_DIRECTION = request.sord,
                ITEMS_PER_PAGE = request.rows,
                PAGE_INDEX = request.PageIndex0Based,
                FILTER_WHERE = filters
            };
            var rows = Exec<SettlementGridItem>("sp_GET_RECYCLING_ORDERS_SETTLEMENT", args);
            var result = new PageOfRows<SettlementGridItem>
            {
                Index0Based = request.PageIndex0Based,
                TotalRows = rows.First().RecyclingOrderId,
                Rows = rows.Skip(1).ToList()
            };

            return result;
        }

        /// <summary>
        /// Indicates if a RecylingOrder has SOs/POs with paid invoices
        /// </summary>
        public InvoicesVoidableStateModel[] CheckSettlementOrderInvoicePaid(Int64 recyclingOrderId)
        {
            var rows = Exec<InvoicesVoidableStateDbModel>("sp_CHECK_ORDER_INVOICE_PAID", new { RECYCLING_ORDER_ID = recyclingOrderId });
            return _invoiceDataService.PrepareCheckCanVoidInvoicesResult(rows);
        }

        public BooleanLookup[] SetRecyclingOrderItemSettlementMode(Int64 recyclingOrderItemId)
        {
            var RecyclingOrderItemsMode = Exec<BooleanLookup>("sp_SET_RECYCLING_ITEMS_FOR_PRICE_CALCULATION", new
            {
                recyclingOrderItemId = recyclingOrderItemId
            }).ToArray();

            return RecyclingOrderItemsMode;
        }

        public IEnumerable<SettlementOrderServiceReportModel> GetSettlementServices(long recyclingOrderId)
        {
            var services = Exec<SettlementOrderServiceReportModel>("sp_GetRecyclingOrderServicesForReport", new { RecyclingOrderId = recyclingOrderId});
            return services;
        }

        public SettlementOrderServiceGridItem GetSettlementService(long recyclingOrderId, int serviceItemId)
        {
            var arg = new
            {
                RecyclingOrderId = recyclingOrderId,
                ServiceItemId = serviceItemId,
            };

            var service = Exec<SettlementOrderServiceGridItem>("[dbo].[sp_GetRecyclingOrderItemService]", arg).SingleOrDefault();

            return service;
        }

        public PageOfRows<SettlementOrderServiceGridItem> GetSettlementServices(long recyclingOrderId, bool hideZeroPriceServices, IPageRequest request, string dbFilters) =>
            GetPage<SettlementOrderServiceGridItem>("dbo.sp_GET_RECYCLING_ORDER_ITEM_SERVICES", new
            {
                RECYCLING_ORDER_ID = recyclingOrderId,
                HIDE_ZERO_PRICE_SERVICES = hideZeroPriceServices,
                ORDER_COLUMN_NAME = DbMappingHelper.GetColumnName<SettlementOrderServiceGridItem>(request.sidx),
                ORDER_DIRECTION = request.sord,
                ITEMS_PER_PAGE = request.rows,
                PAGE_INDEX = request.PageIndex0Based,
                FILTER_WHERE = dbFilters
            }, request.PageIndex0Based, x => x.ServiceItemId);

        public IEnumerable<SettlementOrderOnsiteServiceDetail> GetSettlementServicesDetails(long recyclingOrderId, int serviceTypeId) =>
            Exec<SettlementOrderOnsiteServiceDetail>("dbo.sp_GetRecyclingOrderOnsiteServiceDetails", new { RecyclingOrderId = recyclingOrderId, ServiceTypeId = serviceTypeId });

        public void UpdateOnsiteServiceCategory(Int64? id, int estimatedQty)
        {
            Exec("[rzrapi].[sp_SetInboundOrderServiceCategory]", new
            {
                Id = id,
                EstimatedQty = estimatedQty
            });
        }

        public void UpdateOnsiteServiceLabel(Int64? id, int estimatedQty)
        {
            Exec("[rzrapi].[sp_SetInboundOrderServiceLabel]", new
            {
                Id = id,
                EstimatedQty = estimatedQty
            });
        }

        public void RecalculateInboundOrderServicePricing(Int64? recyclingOrderId, Int64[] serviceTypeIds)
        {
            Exec("[rzrapi].[sp_RecalculateInboundOrderServicePricing]", new
            {
                RecyclingOrderId = recyclingOrderId,
                ServiceTypeIds = ToTableParameter(serviceTypeIds)
            });
        }

        public void SaveAssetSettlementCosts(Int64? id, decimal? dataErasureCost, decimal? auditCost, decimal? dataDestructionCost,
            decimal? miscOverhead, decimal? internalCost, decimal? shippingCost, decimal? adjustmentFX)
        {
            var context = Context.ContextVariables;
            Exec("sp_SetAssetSettlementCosts", new
            {
                AssetId = id,
                DataErasureCost = dataErasureCost,
                AuditCost = auditCost,
                DataDestructionCost = dataDestructionCost,
                MiscOverhead = miscOverhead,
                InternalCost = internalCost,
                ShippingCost = shippingCost,
                AdjustmentFX = adjustmentFX,
                UserId = context.UserId,
                UserIp = context.RequestIntIpV3
            });
        }

        public AssetSettlementCostModel[] GetAssetSettlementCosts(
            Int64 recyclingOrderId,
            Int32 pageIndex,
            Int32 itemsPerPage,
            String orderColumn,
            String orderDirection,
            String filterWhere,
            out Int64 totalRows)
        {
            var mainArgs = new
            {
                RECYCLING_ORDER_ID = recyclingOrderId,
                PAGE_INDEX = pageIndex,
                ITEMS_PER_PAGE = itemsPerPage,
                ORDER_COLUMN_NAME = orderColumn,
                ORDER_DIRECTION = orderDirection,
                FILTER_WHERE = filterWhere
            };

            var rows = Exec<AssetSettlementCostModel>("sp_GetAssetSettlementCosts", mainArgs);

            totalRows = rows.First().AssetId;

            return rows.Skip(1).ToArray();
        }

        /// <summary>
        /// Set the settlement state for recycling order
        /// </summary>
        /// <param name="recyclingOrderId"></param>
        /// <param name="settlementStateId"></param>
        public void SetSettlementState(long recyclingOrderId, int settlementStateId)
        {
            var cv = Context.ContextVariables;
            Exec("dbo.sp_UPD_RECYCLING_ORDER_STATE", new
            {
                RecyclingOrderId = recyclingOrderId,
                SettlementStateId = settlementStateId,
                UserId = cv.UserId,
                UserIp = cv.RequestIntIpV3
            });
        }

        public (long? serviceItemId, string warningMsg) SetRecyclingOrderItemService(SettlementOrderService model) =>
            Exec<(long?, string)>("sp_SET_RECYCLING_ORDER_ITEM_SERVICE", new
            {
                RECYCLING_ORDER_ID = model.RecyclingOrderId,
                RECYCLING_ORDER_ITEM_ID = model.ServiceId,
                SERVICE_TYPE_ID = model.ServiceTypeId,
                DESC = model.Descr,
                //NOTE: explicit Weight and PriceForWeight conversion
                QTY = model.PriceTypeId == 1
                    ? AsWeightToDb(model.Qty)
                    : model.Qty,
                PRICE_FOR_ONE = model.PriceTypeId == 1
                    ? AsPriceForWeightToDb(model.Price)
                    : model.Price,
                NOTES = model.Notes,
                COMMENT = model.Comment,
                USER_ID = Context.ContextVariables.UserId,
                IP = Context.ContextVariables.RequestIntIpV3,
                model.PriceTypeId
            }).FirstOrDefault();

        public decimal? GetServicePriceByQty(SettlementOrderService model) =>
            ExecFunction<decimal?>("fn_float_GetServicePriceByQty", new
            {
                RecyclingOrderId = model.RecyclingOrderId,
                ServiceTypeId = model.ServiceTypeId,
                //NOTE: explicit Weight and PriceForWeight conversion
                Qty = model.PriceTypeId == 1
                    ? AsWeightToDb(model.Qty)
                    : model.Qty,
                Price = model.PriceTypeId == 1
                    ? AsPriceForWeightToDb(model.Price)
                    : model.Price
            });

        #region private properties and fields
        protected IUserAccessDataService _userAccessDataService { set; get; }
        protected IInvoiceDataService _invoiceDataService { set; get; }
        #endregion
    }
}