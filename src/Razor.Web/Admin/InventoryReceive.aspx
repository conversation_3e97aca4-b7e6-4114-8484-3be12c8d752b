<%@ Page Language="C#"
    AutoEventWireup="true"
    CodeBehind="InventoryReceive.aspx.cs"
    Inherits="RazorWeb.Admin.RecieveInventory"
    MasterPageFile="~/Site.Master" %>

<%@ Import Namespace="System.Web.Optimization" %>

<%@ Register TagPrefix="uc" Src="~/Control/MasterItemEditor.ascx" TagName="MasterItemEditor" %>
<%@ Register TagPrefix="uc" Src="~/Control/InventoryReceive/EditorReceiveInventoryItem.ascx" TagName="EditorReceiveInventoryItem" %>
<%@ Register TagPrefix="uc" Src="~/Control/ItemLocationAssignDialog.ascx" TagName="ItemLocationAssigner" %>
<%@ Register TagPrefix="uc" Src="~/Control/InventoryReceive/DialogReceiveInventoryAddons.ascx" TagName="ReceiveInventoryAddonsDialog" %>
<%@ Register TagPrefix="uc" Src="~/Control/DialogInventoryItemAddRemoveParts/DialogInventoryItemAddRemoveParts.ascx" TagName="DialogInventoryItemAddRemoveParts" %>
<%@ Register Src="~/Control/DialogRelocate.ascx" TagPrefix="uc" TagName="DialogRelocate" %>
<%@ Register Src="~/Control/InventoryItemVerification.ascx" TagPrefix="uc" TagName="DialogVerify" %>

<asp:Content ID="Content3" ContentPlaceHolderID="HeadContent" runat="server">
    <meta http-equiv="Pragma" content="no-cache" />

    <%-- Additional --%>

    <%-- CSS --%>
    <%: Styles.Render("~/bundles/ilightbox.css",
        "~/bundles/XtraTemplatePrintDialog.css")%>

    <style type="text/css">
        .inventory_recieve_controls {
            margin-top: 148px !important;
        }

        .div-po-qty-to-receive {
            background-color: #FFB6C1;
            border-radius: 2px;
        }
            .div-po-qty-to-receive span,
            div .container-fa-cellvalue {
                text-indent: -19px;
                display: inline-block;
            }

        .span-po-qty-to-receive {}

        .content-horiz-item > div {
            display: inline-block;
            font-weight: 500;
            /*margin-left: 10px;*/
        }

        .content-vertic-item-data {
            padding: 0;
        }

        .jqgAllocateInventory-table .ui-jqgrid .ui-jqgrid-view .ui-jqgrid-bdiv {
            overflow-x: hidden !important;
        }

        #tab_CreateAddon .righ_container,
        #tab_CreateAddon .left_container {
            margin: 10px 20px 0px;
        }

        #tab_CreateAddon .item_right,
        #tab_CreateAddon .item_right_r,
        #tab_CreateAddon .item_right_l {
            float: left;
            width: auto;
        }

        .content-vertic-item-data .content {
            background: none;
            -webkit-box-shadow: none;
            -moz-box-shadow: none;
            box-shadow: none;
            padding: 0px 20px;
        }

        .buttons-block {
            background: none;
            padding: 0 20px;
            margin: 0 0 0px 0;
            -webkit-box-shadow: none;
            -moz-box-shadow: none;
            box-shadow: none;
            position: relative;
        }

        .tabs-header + div {
            border: 1px solid #CECECE !important;
            overflow: hidden;
        }

        .first-content-table {
            padding: 0;
        }

        #div_AddOnsDialog .tab-status-wr {
            margin-bottom: -2px !important;
        }

            #div_AddOnsDialog .tab-status-wr li.ui-state-active .ui-tabs-anchor,
            #div_AddOnsDialog .tab-status-wr li.active .tabStatus {
                background: #fff !important;
                border: 1px solid #CECECE !important;
                border-bottom: none !important;
                margin: 0 !important;
                padding: 12px 25px !important;
                font-size: 16px !important;
                cursor: default;
                -webkit-box-shadow: none !important;
                -moz-box-shadow: none !important;
                box-shadow: none !important;
            }

        #div_AddOnsDialog .ui-state-default .ui-tabs-anchor {
            background: #E5E5E5 !important;
            border: 1px solid #CECECE !important;
            border-bottom: none !important;
            padding: 8px 25px !important;
            margin: 0 !important;
            margin-top: 8px !important;
            font: 700 12px/15px Tahoma, Geneva, Kalimati, sans-serif !important;
            -webkit-box-shadow: none !important;
            -moz-box-shadow: none !important;
            box-shadow: none !important;
        }
        /*#ac_AddOn_Cosmetics:hover + ul li > ul {
            display: block!important;
        }*/
        .attribute-notes-unique > label,
        .righ_container .attribute label {
            font-size: 12px;
        }

        .item_right_l #ta_AddOn_Notes {
            min-height: 50px;
        }

        .buttons-block .button.btnSelectClass,
        .buttons-block .button.btnSelectClass.disabled {
            padding: 5px 10px !important;
            margin: 0 10px 5px 0 !important;
        }

        .ui-menu-item {
            float: left;
            cursor: pointer;
        }

            .ui-menu-item > ul {
                border: 1px solid #aaaaaa;
                width: 80px;
                /*-webkit-shadow: 4px 4px 4px 4px rgba(0, 0, 0, .2);
                -moz-box-shadow: 4px 4px 4px 4px rgba(0, 0, 0, .2);
                box-shadow: 4px 4px 4px 4px rgba(0, 0, 0, .2);*/
            }

                .ui-menu-item > ul > li:hover {
                    background: #E5E5E5;
                }

        .ui-menu {
            float: left;
        }

        .ui-autocomplete.ui-menu.ui-widget {
            width: 345px !important;
        }
        #div_EbayCategory .ui-autocomplete.ui-menu.ui-widget {
            width: 760px !important;
        }

        .ui-pg-table select {
            width: 48px;
        }

        .ui-autocomplete.ui-menu .ui-menu-item a {
            text-transform: uppercase !important;
        }

        .mmdialog.ui-dialog-content .dimensions-popup label {
            width: auto;
            display: inline-block;
            float: none;
            width: auto;
        }

        .item_right_l,
        .item_right_r,
        .item_right {
            float: left !important;
            width: auto !important;
        }

        .attribute-notes-unique {
            float: left !important;
        }

        /* style for dialog "Send To Recycling" */
        .send-recycling {
        }

            .send-recycling .top-control {
                /*background-color: #00ced1;*/
                /*height: 70px;*/
            }

                .send-recycling .top-control .left {
                    /*background-color: #008000;*/
                    float: left;
                }

                .send-recycling .top-control .right {
                    /*background-color: #aaaaaa;*/
                    float: right;
                }

                .send-recycling .top-control label {
                    font-size: 2em;
                    margin-left: 15px;
                }

                .send-recycling .top-control .info {
                    font-size: 1.2em;
                    margin: 0;
                }

                .send-recycling .top-control .search {
                    margin: 5px 0 0;
                }

            .send-recycling .grid-content {
            }

            .send-recycling .ui-state-default,
            .send-recycling .ui-widget-content .ui-state-default,
            .send-recycling .ui-widget-header .ui-state-default {
                text-shadow: none;
            }

            .send-recycling .ui-widget input {
                width: 45px;
            }
            #dlg_SendToRecycling .label-bold {
                font-weight: bold;
            }
            #dlg_SendToRecycling .info {                
                font-size: 14px;
                display: inline-block;
                padding-right: 10px;
                min-height: 28px;
            }
            #dlg_SendToRecycling .left1 {
                float: left;
            }
            #dlg_SendToRecycling .left .left1 .info {
                min-width: 72px;
            }

            #dlg_SendToRecycling .left2 {
                float: left;
                margin-left: 10px;
            }

            #dlg_SendToRecycling .left2 select {
                min-width: 122px;
                max-width: 252px;
            }

            #dlg_SendToRecycling .left .left2 .info {
                min-width: 72px;
            }

            #dlg_SendToRecycling .grid-stat {
                float: right;
            }

            #dlg_SendToRecycling .grid-stat label {
                margin-left: 12px;
            }

            .send-recycling .top-control label {
                font-size: 16px;
            }
            .settle-grid-wr .customelement {            
                width: 100%;
                box-sizing: border-box;
            }
            
            .ui-jqgrid tr.ui-row-ltr td a.btnSelectClass {
                font-size: 10px!important;
                padding: 0!important;
                width: 24px!important;
                height: 24px!important;
                margin: 0!important;
                text-align: center!important;
            }
            .ui-jqgrid tr.ui-row-ltr td a.btnSelectClass img {
                margin: 3px 0 0 0!important;
                font-size: 10px!important;
                padding: 0!important;
                display: inline-block!important;
            }
            #div_EbayCategory .ui-autocomplete.ui-menu.ui-widget {
                  max-height: 586px;
            }         
            #breakDownDialog .content-data {
                margin: 0;
            }
            #breakDownDialog .content-horiz-item .content-horiz-item-row {
                margin-left: 12px;
            }            
            #breakDownDialog .content-horiz-item .content-horiz-item-row:first-child {
                margin-left: 0px;
            }
            /*#div_BulkChangeDialog > div .righ_container {
                -webkit-column-count: 2;
                -moz-column-count: 2;
                column-count: 2;
                -moz-column-fill: auto;
                column-fill: auto;
                height: 423px;
                width: 667px;
                overflow-x: auto;
            }*/

            .po-search {
                margin-left: 15px;
                margin-top: 3px;
            }

            .po-search input[type="text"]{
                margin-top: 0px;
                margin-bottom: 0;
                vertical-align: middle;
            }

            .po-search button {
                margin-top: 0px;
            }

            .po-search label {
                margin-right: 6px
            }

            .dialog-div-never-ask {
                width: auto !important;
                position: relative;
                bottom: -10px;
            }

            .dialog-div-never-ask input[type="checkbox"] {
                float: left;
                margin-top: 4px;
                margin-left: 1px;
            }

            .dialog-div-never-ask label {
                width: auto !important;    
            }
        
            .tabs-table-item a.ui-tabs-anchor, ul.tabs-table-title li a {
                padding: 8px 20px !important;
            }

    .bulk-delete-items-dialog {}
        .bulk-delete-items-dialog h5 {
            margin: 14px 0 4px;
            font-size: 14px;
        }
        .bulk-delete-items-dialog table {
             border-collapse: collapse;
             margin-bottom: 10px;
        }
            .bulk-delete-items-dialog table td {
                padding: 2px 7px 2px 0;
                vertical-align: middle;
            }
            .bulk-delete-items-dialog table td input[type="checkbox"] {
                margin: 0;
                vertical-align: -2px;
            }
            
    #breakDownDialog .content-horiz-item { 
        float: none;
        margin: 0 0 0 -10px; 
    }
    #breakDownDialog .content-horiz-item-row-left { width: 90px; }
    #breakDownDialog .crm-popup .content-horiz-item-row-right { width: 156px; }
    #breakDownDialog .content-horiz-item .content-horiz-item-row { 
        display: block;
        margin: 10px 0 0;
        float: left;
    }
    #breakDownDialog .content-horiz-item-row-left { margin-left: 10px; }

    .controls {
        margin-top: 0;
        margin-left: 10px;
        padding-right: 18px;
        box-sizing: border-box;
        max-width: 1858px;
    }
    .controls .buttons.nomargin {
        width: auto;
    }
    .controls .buttons.nomargin .m-btn { margin: 4px 0 0 2px; }

    .tr-not-from-order-item {
        background: repeat-x scroll 50% 50% #96DBF0 !important;
    }

    @media (max-width: 1400px) {
        
        .inventory-menu input { 
            margin-left: 3px;
            font-size: 11px;
            padding-left: 4px;
            padding-right: 4px;
        }

        .po-search {
            margin-left: 7px;
        }
        .po-search label {
            margin-right: 4px;
            font-size: 12px;
        }
        .po-search input[type="text"] {
            width: 160px !important;
        }
        .po-search button {
            margin-top: 3px;
            padding: 4px 5px;
            vertical-align: middle;
        }
        
    }

    .receive-against-bar { padding: 5px 0; }
        .receive-against-bar-button {
            float: left;
        }
            .receive-against-bar-button .btnSelectClass { margin: 0; }
        .receive-against-bar-fields {
            float: left;
            margin-left: 70px;
        }
            .receive-against-bar-fields > div { padding-bottom: 10px; }
            .receive-against-bar-fields label {
                display: inline-block;
                vertical-align: -1px;
            }
            .receive-against-bar-fields input[type="checkbox"] {
                margin: 0 4px;
                vertical-align: middle;
            }
            .receive-against-bar-fields input[type=text] { 
                vertical-align: middle; 
                margin-left: 4px;
            }
            .receive-against-bar-fields select { height: 23px; }
            
        .serial-duplicate {
            margin-left: -180px;
            margin-top: 22px;
            display: block;
            color: blue;
        }
        
        .uid-duplicate {
            margin-left: -180px;
            margin-top: 22px;
            display: block;
            color: red;
        }

        #PartNumberSearch {
            width: 314px;
            margin-left: -1px;
            margin-bottom: 5px;
        }

        #dd_BulkChange_AttrType {
            width: 274px;
        }

        label[for="ddlPrintersConfirm"] {
            width: 50px;
        }

        #ddlPrinters,
        #ddlLabelSize,
        #ddl_Categories,
        #ddl_Statuses,
        {
            max-width: 244px;
        }
        #invReceiveCont {
            overflow: auto;
        }
</style>

    <%-- JS --%>
    <%: Scripts.Render("~/bundles/lightbox.js")%>

    <%--Xtra templates scripts--%>
    <%: Scripts.Render("~/bundles/XtraTemplatePrintDialog.js") %>
    <%--Xtra templates scripts--%>

    <%: Scripts.Render("~/bundles/InventoryReceive.js")%>
    <%: Scripts.Render("~/bundles/InventoryReceive/BulkInsert.js")%> 

    <script type="text/javascript">
        window.masterItemEditor = null;
        var recieveInventory = null,
            locationAssigner = null;

        function controlsWidth() {

            var windowWidth = $(window).width();

            $('.controls').css('width', windowWidth);

        }

        $(document).ready(function () {
            window.PageDefaults = new PageDefaults([
                new Ractive.components.DefaultsReceiveInventory({ el: 'div_UserSpecificPageSettings' })
            ]);
            locationAssigner = new ItemLocationAssignDialog();
            recieveInventory = new InventoryReceive();            

            if ($(window).width() > 1024) {
                controlsWidth();
            }
        });

        $(window).resize(function () {
            if ($(window).width() > 1024) {
                controlsWidth();
            }
        });

    </script>
</asp:Content>

<asp:Content ID="Dialogs1" ContentPlaceHolderID="Dialogs" runat="server">
    <div id="div_CopyInventoryContainer"></div>
    <div id="div_bulkRelocatePlaceholder" style="display: none"></div>
    <div style="display:none;" id="div_DialogDeleteWithPermissionsCheck"></div>
    <uc:DialogRelocate runat="server" ID="DialogRelocate1"></uc:DialogRelocate>
    <uc:DialogInventoryItemAddRemoveParts runat="server" ID="DialogInventoryItemAddRemoveParts1"></uc:DialogInventoryItemAddRemoveParts>
    <uc:DialogVerify runat="server" ID="DialogVerify"></uc:DialogVerify>
    <div id="div_bulkDeleteQueue" style="display:none">
        <div style="margin: 20px 0 10px;">
            <button class="button btnSelectClass" id="btn_BulkDelete_Delete" style="margin: 0">Delete selected</button>
            <select style="float:right; width:180px" id="dd_Warehouse">
            </select>
            <input type="text" id="dd_BulkDelete_Location" placeholder="Enter Location" style="float: right; margin-right:15px;"/>
            <input type="text" id="tb_BulkdDelete_SerialNumber" placeholder="Scan UID/Serial" style="float: right; margin-right:50px"/>
        </div>
        <table id="jqg_BulkDelete_ReceiveInventory"></table>
    </div>
    <div id="print_queue_dialog" class="print-queue-dialog" style="display: none;">
        <div class="inline-block">
            <span>Select a printer</span>
            <select id="ddl_printers"></select>
            <select id="ddl_LabelSize"></select>
        </div>
        <div class="table-holder jqgAddScroll">
            <table id="jqt_print_items"></table>
            <div id="jqp_print_items"></div>
        </div>
    </div>

    <div id="audit_history_dialog" class="print-queue-dialog" style="display: none;">
        <div class="table-holder">
            <table id="jqg_AuditHistoryD"></table>
            <div id="jqp_AuditHistoryD"></div>
        </div>
    </div>
    
    <div id="move_to_po_dialog" style="display: none;">
        <div style="margin-top: 20px;">
            <label for="ac_MoveToPurchaseOrder" class="info">Purchase Order</label>
            <input type="text" id="ac_MoveToPurchaseOrder" style="width: 260px;"/>
        </div>
    </div>

    <%--Send To Recycling Dialog--%>
    <div id="dlg_SendToRecycling" class="loc-popap clearfix send-recycling" style="display: none">

        <div class="top-control clearfix">
            <div class="left">
                <div class="clearfix">
                    <div class="left1">
                        <div>
                            <label class="info">Tare</label>
                            <input id="tb_SetTare" type="text" style="width: 100px;"/>
                        </div>
                        <div class="required">
                            <label class="info">Commodity</label>
                            <input id="tb_CommoditiesSearch" type="text" style="width: 170px; height: 17px;"/>
                        </div>
                    </div>
                    <div class="left2">
                        <div>
                            <label class="info">Warehouse</label>
                            <select id="dd_ToScrapWarehouse"></select>
                        </div>
                        <div>
                            <label class="info">Printer</label>
                            <select id="dd_RIListPrinters"></select>                            
                        </div>
                    </div>
                </div>
<%--                <div style="  margin-top: 23px;">
                    <label class="info"><b id="lb_selectitems">0</b> Item(s) selected</label>
                </div>--%>
            </div>
            <div class="right">
                <div class="content-horiz-item search-block clearfix" style="float: right; margin-top: 26px; margin-bottom: 8px;">
                    <input id="tb_SendToRecyclingSearch" class="search" type="text" placeholder="Search ..." />
                </div>
            </div>
        </div>
        <div class="grid-content">
            <table id="jqg_SendToRecycling"></table>
            <div id="jqp_SendToRecycling"></div>
            
            <div style="margin-top: 6px;">
                <div style="float: left;">
                    <label class="info" for="lb_selectitems"><span class="label-bold" id="lb_selectitems">0</span> Item(s) selected</label>
                </div>
                <div class="grid-stat">
                    <label for="lb_gross">Gross:</label> <span class="label-bold" id="lb_gross">0.00</span>
                    <label for="lb_tare">Tare:</label>   <span class="label-bold" id="lb_tare">0.00</span>
                    <label for="lb_net">Net:</label>     <span class="label-bold" id="lb_net">0.00</span>
                </div>
            </div>
        </div>
    </div>

    <uc:MasterItemEditor runat="server" ID="ucMasterItemEditor" />
    <div id="div_PendingPurchaseOrderItems" style="overflow:auto;">
        <div class="receive-against-bar clearfix">
            <div class="receive-against-bar-button">
                <button type="button" id="bt_PendingPurchaseOrderItems_ReceiveSelected" class="button btnSelectClass">Receieve Selected</button>
            </div>

            <div class="receive-against-bar-fields">
            <div>
                <label for="cb_PendingPurchaseOrderItems_ShowOnlyNotReceived">Show Items that have not been received</label>
                <input type="checkbox" id="cb_PendingPurchaseOrderItems_ShowOnlyNotReceived" />
            </div>
            <div>
                <input type="checkbox" id="cb_PendingPurchaseOrderItems_ChooseDefLocation" />
                <select id="tb_PendingPurchaseOrderItems_Warehouse" style="width: 140px"></select>
                <input id="tb_PendingPurchaseOrderItems_Location" type="text" value="" style="width: 140px" />
                <label for="tb_ItemInventorySerialOrUid">Scan repair serial or UID:</label>
                <input id="tb_ItemInventorySerialOrUid" type="text" value="" style="width: 140px" />
            </div>
            </div>
            
            <div class="receive-against-bar-button" style="float: right">
                <button type="button" id="bt_MarkReadyToClose" class="button btnSelectClass">Mark ready to close</button>
            </div>

            <div class="receive-against-bar-button" style="float: right; margin-right: 15px;">
                <button type="button" id="bt_SetDelivered" class="button btnSelectClass">Delivered</button>
            </div>
        </div>
        <div>
            <!--search-->
        </div>
        <table id="g_PendingPurchaseOrderItems"></table>
        <div   id="p_PendingPurchaseOrderItems"></div>
        <div id="l_summary" style="margin-top: 4px; float: right;"></div>
    </div>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="PageContent" runat="Server">
    <div class="clearfix inventory-menu">
        <div class="left">
            <input type="button" class="button btnSelectClass" id="bt_Clear" value="Clear" />

            <% if (PagePermissions.canDelete)
               { %>
                <input type="button" class="button btnSelectClass" data-canDelete="true" id="bt_BulkDelete" value="Bulk Delete" />
            <% } %> 
            <input type="button" class="button btnSelectClass disabled" id="print_queue" value="Print Labels" />
            <input type="button" class="button btnSelectClass disabled" id="bt_PrintNew" value="Print/Download New" />
            <% if (PagePermissions.canEdit)
               { %>
                <input type="button" class="button btnSelectClass disabled" data-canEdit="true" id="bt_BulkInsert" value="Bulk Insert" />
                <input type="button" class="button btnSelectClass disabled" data-canEdit="true" id="bt_BulkChange" value="Bulk Change" />
                <input type="button" class="button btnSelectClass disabled" data-canEdit="true" id="bt_BulkRelocate" value="Bulk Relocate" />
            <% } %> 
            <input type="button" class="button btnSelectClass disabled" id="bt_Addons" value="Addons" />
            <% if (PagePermissions.canEdit)
               { %>
                <input type="button" class="button btnSelectClass disabled" data-canEdit="true" id="bt_SendToRecycling" value="Send To Recycling" disabled="disabled" />
            <% } %> 
            <input type="button" class="button btnSelectClass disabled" id="bt_BreakDown" value="Break Down/Tear Down" disabled="disabled" />
            <input type="button" class="button btnSelectClass disabled" id="bt_DialogAddRemoveParts" value="Add/Remove Parts" disabled="disabled" />
            <input type="button" class="button btnSelectClass" id="bt_Relocate" value="Relocate" />          
            <input type="button" class="button btnSelectClass disabled" id="bt_MoveToPO" value="Move to PO" disabled="disabled" />
        </div>
        <div class="left po-search">
            <label for="ac_PurchaseOrdersPendingReceive">PO #</label>
            <input type="text" id="ac_PurchaseOrdersPendingReceive"/>
            <button id="bt_ClearPurchaseOrder"     class="button btnSelectClass fa fa-close"></button>
            <button id="bt_ViewPurchaseOrderItems" class="button btnSelectClass disabled fa fa-search"></button>
        </div>         
    </div>
    <div class="content-wrapper inventory_recieve_controls" id="div_inputs">

        <div id="divNewValContainer" class="newValContainer" style="display: none">
            <div id="btn_close" class="close">X</div>
            <input id="inpNewValue" type="text" class="upper-case b-input-field m0" />
        </div>

        <div class="clearfix">

            <div class="left_container" style="width: 470px;">                
                <div class="attribute clearfix tb_Heci_Container" style="margin-bottom: 5px; ">
                    <div class="attribute-notes-unique clearfix">
                        <label for="ac_RecordProductCode">
                            <span style="float: left; margin-right: 6px; margin-top: 2px;">Code</span>
                            <span>
                                <select id="dd_RecordProductCodeType" style="float: left; min-width: 100px; margin: 3px 0 0 0;" data-no-value-stamp>
                                    <option value="1">CLEI/HECI</option>
                                </select>
                            </span>
                        </label>                        
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="ac_RecordProductCode" type="text" class="left" />
                        </div>
                    </div>
                </div>

                <div class="attribute clearfix">
                    <input id="PartNumberSearch" type="text" />
                </div>
                <div class="attribute clearfix required">
                    <div class="attribute-notes-unique clearfix">
                        <label for="MasterItemId">Model</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="MasterItemId" type="text" class="upper-case left" />
                        </div>
                        <div class="item_right_r clearfix">
                            <button id="bt_MasterItemId_Upd" class="btnSelectClass miscLuVal left">v</button>
                        </div>
                    </div>
                </div>

                <div class="attribute clearfix required">
                    <div class="attribute-notes-unique clearfix">
                        <label for="MFGId">Manufacturer</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="MFGId" type="text" class="upper-case left" />
                        </div>
                        <%--<div class="item_right_r clearfix">
                            <button id="bt_MFGId_AttrReload" data-for-id="MFGId" class="btnSelectClass miscLuVal left">
                                <img alt="R" src="<%=ResolveUrl("~/Images/refresh.png")%>" style="margin-top: 3px;" />
                            </button>
                        </div>--%>
                    </div>
                </div>

                <div class="attribute clearfix tb_IpnMpn_Container">
                    <div class="attribute-notes-unique clearfix">
                        <label for="MPN">MPN</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="MPN" type="text" class="upper-case left" />
                        </div>
                        <div class="item_right_r clearfix">
                        </div>
                    </div>
                </div>

                <div class="attribute clearfix tb_IpnMpn_Container">
                    <div class="attribute-notes-unique clearfix">
                        <label for="IPN">IPN</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="IPN" type="text" class="upper-case left" />
                        </div>
                        <div class="item_right_r clearfix">
                        </div>
                    </div>
                </div>

                <div class="attribute clearfix required">
                    <div class="attribute-notes-unique clearfix">
                        <label for="CVCode">Customer Name</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="CVCode" type="text" class="upper-case left" />
                        </div>
                        <div class="item_right_r clearfix">
                        </div>
                    </div>
                </div>

                <div class="attribute clearfix required">
                    <div class="attribute-notes-unique clearfix">
                        <label for="UniqueId">Unique Identifier</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="UniqueId" type="text" maxlength="50" class="left" />
                        </div>
                        <div class="item_right_r clearfix">
                            <button id="btnGenerateUI" data-for-id="UniqueId" class="btnSelectClass miscLuVal left">
                                <img alt="R" src="<%=ResolveUrl("~/Images/refresh.png")%>" style="margin-top: 3px;" />
                            </button>
                            <a name="bt_AuditInventory" data-prop-name="UniqueId" for="#UniqueId" href="#" class="btn-default-loupe left" style="display: block;"></a>
                            <a name="bt_AuditHistory"   data-prop-name="UniqueId" for="#UniqueId" href="#" class="btn-monitor-loupe left" style="display: block;"></a>
                            <span id="er_UniqueId" class="uid-duplicate" style="display: none;">
                                <span>Duplicate</span>
                                <span id="span_Return_inventory_item" style="display: none;"> - item Sold. <a href="#">Click to return</a> the item</span>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="attribute clearfix required">
                    <div class="attribute-notes-unique clearfix">
                        <label for="Serial">Serial</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="Serial" type="text" maxlength="50" class="upper-case left" />
                        </div>
                        <div class="item_right_r clearfix">
                            <button id="btnGenerate" data-for-id="Serial" class="btnSelectClass miscLuVal left">
                                <img alt="R" src="<%=ResolveUrl("~/Images/refresh.png")%>" style="margin-top: 3px;" />
                            </button>
                            <a name="bt_AuditInventory" data-prop-name="Serial" for="#Serial" href="#" class="btn-default-loupe left" style="display: block;"></a>
                            <a name="bt_AuditHistory"   data-prop-name="Serial" for="#Serial" href="#" class="btn-monitor-loupe left" style="display: block;"></a>
                            <span id="er_Serial" style="display: none;" class="serial-duplicate">Duplicate</span>
                        </div>
                    </div>
                </div>

                <div class="attribute clearfix">
                    <div class="attribute-notes-unique clearfix">
                        <label for="Qty">Quantity</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="Qty" type="text" class="upper-case left" />
                        </div>
                        <div class="item_right_r clearfix">
                        </div>
                    </div>
                </div>

                <div class="attribute clearfix toggle-item">
                    <div class="attribute-notes-unique clearfix">
                        <label for="tb_RecvPrice">Received Price</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="tb_RecvPrice" type="text" class="upper-case left" />
                        </div>
                        <div class="item_right_r clearfix">
                        </div>
                    </div>
                </div>

                <div class="attribute clearfix required">
                    <div class="attribute-notes-unique clearfix">
                        <label for="RealCondition">Condition</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="RealCondition" type="text" typeid="8" class="upper-case left" />
                        </div>
                        <div class="item_right_r clearfix">
                        </div>
                    </div>
                </div>                

                <div class="attribute clearfix">
                    <div class="attribute-notes-unique clearfix">
                        <label for="dd_Warehouses">Warehouse</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <select id="dd_Warehouses" class="upper-case left" style="width: 177px;"></select>
                        </div>
                    </div>
                </div>

                <div class="attribute clearfix">
                    <div class="attribute-notes-unique clearfix">
                        <label for="Location">Assign Location</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="Location" typeid="15" type="text" class="upper-case left" />
                        </div>
                        <div class="item_right_r clearfix">
                            <button id="bt_SelectLocation" class="button btnSelectClass left disabled">v</button>
                            <button id="bt_MainLocationCheckSpace" data-for-id="Serial" class="btnSelectClass miscLuVal left">
                                <img alt="R" src="<%=ResolveUrl("~/Images/refresh.png")%>" style="margin-top: 3px;" />
                            </button>

                            <div class="checkbox-btn">
                                <input type="checkbox" id="cb_InventoryReceive_Location_IsLocked" class="css-checkbox" title="Lock the location" data-no-value-stamp/>
                                <label for="cb_InventoryReceive_Location_IsLocked" class="css-label"></label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="attribute clearfix">
                    <div class="attribute-notes-unique clearfix">
                        <label for="dd_ItemType">Special Item</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <select id="dd_ItemType" style="width: 177px;">
                                <option value="0">None</option>
                                <option value="1">Unique Item</option>
                                <option value="2">Virtual</option>
                            </select>
                        </div>
                        <div class="item_right_r clearfix">
                        </div>
                    </div>
                </div>
                <div class="attribute clearfix">
                    <div class="attribute-notes-unique clearfix">
                        <label for="AssetTag">Tag</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <input id="AssetTag" type="text" class="upper-case left" />
                        </div>
                        <div class="item_right_r clearfix">
                        </div>
                    </div>
                </div>
                <div class="attribute clearfix">
                    <div class="attribute-notes-unique clearfix">
                        <label for="Notes" style="margin-bottom: 10px;">Notes</label>
                    </div>
                    <div class="item_right clearfix">
                        <div class="item_right_l clearfix">
                            <textarea id="Notes" rows="3" cols="25"></textarea>
                        </div>
                        <div class="item_right_r clearfix">
                        </div>
                    </div>
                </div>

            </div>

            <div id="invReceiveCont" class="righ_container">
                <%-- dynamic attributes --%>
                <div id="controlsContainer"></div>
                <%-- inventory files uploader --%>
                <div id="uploadContainer">
                    <iframe id="if_InventoryFileUpload" src="<%=ResolveUrl("~/Images/ajax-loader_small.gif")%>" style="width: 380px; height: auto; min-height:100px; border:none;"></iframe>
                </div>
            </div>

            <div class="item_right_l category-span-custom-wr clearfix">
                <div class="clearfix">
                    <span style="color:blue;">* SKU Affecting Attribute</span>
                    <span style="color:red;">* Mandatory Attribute</span>
                </div>
                <div class="attribute-notes-unique clearfix">
                    <span>Categories</span>
                </div>

                <div style="float: left;" id="span_ItemCategory">
                    <span class="category-span">none</span>
                </div>
            </div>

        </div>       
        
    </div>

    <div class="content-wrapper clear controls clearfix">
        <div class="buttons nomargin">
            <% if (PagePermissions.canEdit)
               { %>
                <input class="m-btn m-blue" id="bt_Accept" style="display: none;" type="button" value="Accept" data-canEdit="true" />
                <input class="m-btn m-blue" id="bt_CreateAndClear" type="button" value="Create & Clear" data-canEdit="true" />
                <input class="m-btn m-blue" id="bt_Create" type="button" value="Create" data-canEdit="true" />
            <% } %>
                <input class="m-btn m-blue" id="bt_ClearAttributes" type="button" value="Clear Attributes" data-canEdit="true" />
            <% if (PagePermissions.canDelete)
               { %>
                <input class="m-btn m-black" id="bt_Delete" style="display: none;" type="button" data-canDelete="true" value="Delete" />
            <% } %>
            <% if (PagePermissions.canEdit)
               { %>
                <%-- To enable this button change it id to bt_MakeActive --%>
                <input class="m-btn m-black" id="bt_MakeActive_temporarily_disabled" style="display: none;" type="button" value="Make Active" data-canEdit="true" />
            <% } %>
            <input class="m-btn m-black" id="bt_Picture" type="button" style="display: none;" value="Picture" />
            <input class="m-btn m-black" id="bt_DialogLog" type="button" style="display: none;" value="Log" />
            <input class="m-btn m-black" id="bt_SetAuditStatus" type="button" style="display: none;" value="Not found" />
        </div>

        <div id="mainPrintContainer" class="left" style="display: none;">
            <a id="aPrint" href="#">print »</a>
            <div id="printContainer" class="printContainer" style="display: none; width: auto;">
                <div class="printers">
                    <select id="ddlPrinters"></select>
                    <select id="ddlLabelSize"></select>
                </div>

                <div class="inline">
                    <input id="btnPrintToServer" type="button" class="m-btn m-blue" value="Print Label" />
                    <button id="aLocalPrint" type="button" class="m-btn m-blue" target="_blank" style="display: none;">Print Dialog</button>
                    <button id="aDownloadLabel" type="button" class="m-btn m-blue" target="_blank">Download Label</button>
                    <span id="spnPrintOnServerMsg" class="smallMsg" style="display: none"></span>
                </div>
            </div>
        </div>
    </div>

    <div class="content-wrapper inventory_recieve_tables">
        <p style="margin-top: 0;">
            <select id="ddl_Categories">
                <option value="1">Not Specified</option>
            </select>
            <select id="ddl_Statuses">
                <option value="-1">All statuses</option>
            </select>
        </p>
        <div id="print_area" class="jqgAddScroll">
            <table data-search-control="searchBar" id="jqg_RecieveInventory"></table>
            <div id="jqp_RecieveInventory"></div>
        </div>
        <%--<div id="export_Nav" class="right_container">
             <div class="inline">
                <input id="bt_Print" type="button" class="button btnSelectClass" value="Print"/>                   
             </div>
        </div>--%>
    </div>


    <div id="create_dialog_holder" style="display: none;">
        <div class="full-width clearfix" style="padding: 10px 0 10px 0;">
            <div class="left" style="width: 177px;">

                <div class="clearfix" style="width: 160px; float: left;">
                    <span class="cap">Model</span>
                    <input id="inpModel" type="text" />
                </div>

                <span style="display: block; float: left; margin: 23px 1px 0 9px;">x</span>

                <div class="clearfix" style="float: left;">
                    <span class="cap">Manufacturer</span>
                    <input id="inpMFG" type="text" />
                </div>

            </div>
            <div class="left" style="width: 160px;">
                <label for="inpQuantity" class="cap">Quantity</label>
                <input id="inpQuantity" type="text" />
                <label for="inpCustomerName" class="cap" style="line-height: 1.6;">Customer</label>
                <input id="inpCustomerName" type="text" />
            </div>
            <div class="left" style="width: 180px; padding-top: 22px; margin-left: 20px;">
                <input id="cb_AutoUId" type="checkbox" checked="checked" />
                <label for="cb_AutoUId" class="cap">Auto generate UId</label><br />
                <input id="cb_AutoSerial" type="checkbox" checked="checked" />
                <label for="cb_AutoSerial" class="cap">Auto generate Serial #</label>
            </div>
            <div class="left" style="width: 130px; padding-top: 68px;">
                <input id="generateItems" style="margin-top: 0; float: right;" class="button btnSelectClass" value="Generate" type="button" />
            </div>
        </div>
        <div class="clear table jqgAddScroll-y">
            <table id="jqg_NewItems"></table>
            <%--<div id="jqp_NewItems"></div>--%>
        </div>

        <input id="btnSave" class="button btnSelectClass" type="button" value="Save" />
        <input id="btnCancel" class="button btnSelectClass" type="button" value="Cancel" />
    </div>

    <div id="breakDownDialog" class=" clearfix" style="display: none">
        
    </div>

    <div id="breakdownQuantityDialog" class=" clearfix" style="display: none">
        
    </div>
    <div id="div_DialogItemInventoryLog" class=" clearfix" style="display: none">
        <table id="g_DialogItemInventoryLog"></table>
        <div id="p_DialogItemInventoryLog"></div>
    </div>

    <div id="div_LocationsAssigner" style="display: none;">
        <uc:ItemLocationAssigner ID="UcItemLocationAssignDialog" runat="server" />
    </div>


    <uc:ReceiveInventoryAddonsDialog ID="UcReceiveInventoryAddonsDialog" runat="server" />
    <div id="div_BulkInsertDialog" style="display: none;">
        <uc:EditorReceiveInventoryItem Prefix="BulkInsert" ID="UcEditorReceiveInventoryItemBulkInsert" runat="server" />
    </div>

    <div id="div_BulkChangeDialog" style="display: none;">
        <select id="dd_BulkChange_AttrType"></select>
        <uc:EditorReceiveInventoryItem Prefix="BulkChange" BulkChangeMode="true" ID="UcEditorReceiveInventoryItemBulkChange" runat="server" />
    </div>

    <div id="div_LabelPrintConfirmDialog" style="display: none; max-width: 500px;">
        Would you like to print a label?<br/>
        <label for="ddlPrintersConfirm">Printer: </label>
        <select class="ddlPrintersConfirm"></select>
    </div>

    <div style="display: none;" id="div_Gallery"></div>

    <%--Xtra templates print dialog--%>
    <div id="div_reportPrintDialog" class=" clearfix" style="display: none"></div>

</asp:Content>

