using System;
using Razor.Common.Core.UserAccess.Enums;
using Razor.Data.Core.Enums;
using RazorWeb.Baseclass;
using RazorWeb.Helpers;

namespace RazorWeb.Admin.Payments
{
    public partial class ArPayment : SecuredBasePage
    {
        public ArPayment()
        {
            FunctionalSectionId = FunctionalSections.PgMakeArPayment;
        }

        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            this.HighlightMenuItem(new[] { FunctionalSections.GrSales }, FunctionalSectionId);
        }
    }
}