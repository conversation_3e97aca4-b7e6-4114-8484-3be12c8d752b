using System;
using System.Configuration;
using System.Data.Entity.Core.EntityClient;
using Razor.Common.Base.Configuration;
using Razor.Common.Base.Context.Models.Connections;

namespace Razor.Common.Base.Context
{
    public class ConnectionStringsConfig: ConfigSectionBase
    {
        private string _configDbConnectionString;
        private string _channelsDbConnectionString;
        private string _financeDbConnectionString;

        public string ConfigDbConnectionString
        {
            get
            {
                if (_configDbConnectionString == null)
                {
                    _configDbConnectionString = ReadConnectionString(ConnectionStringNames.ConfigDb);
                }
                return _configDbConnectionString;
            }
        }

        public string ChannelsDbConnectionString
        {
            get
            {
                if (_channelsDbConnectionString == null)
                {
                    _channelsDbConnectionString = ReadConnectionString(ConnectionStringNames.ChannelsDb);
                }
                return _channelsDbConnectionString;
            }
        }

        public string FinanceDbConnectionString
        {
            get
            {
                if (_financeDbConnectionString == null)
                {
                    _financeDbConnectionString = ReadConnectionString(ConnectionStringNames.FinanceDb);
                }
                return _financeDbConnectionString;
            }
        }

        public string GetConnectionString(MultyClientDatabases toDatabase)
        {
            switch (toDatabase)
            {
                case MultyClientDatabases.SalesChannel:
                    return ChannelsDbConnectionString;
                case MultyClientDatabases.Config:
                    return ConfigDbConnectionString;
                default:
                    throw new ArgumentOutOfRangeException(nameof(toDatabase), toDatabase, null);
            }
        }

        private static string ReadConnectionString(string name)
        {
            var setting = ConfigurationManager.ConnectionStrings[name];
            if (setting == null)
                return null;

            var connectionString = ExtractFromEfConnectionString(setting.ConnectionString);
            return connectionString;

            string ExtractFromEfConnectionString(string cs)
            {
                // is DbConnection string
                if (cs != null && cs.Contains("provider connection string"))
                {
                    var sb = new EntityConnectionStringBuilder(cs);
                    cs = sb.ProviderConnectionString;
                }
                return cs;
            }
        }
    }
}
