InvoiceTypes = {
    Sales       : 1,
    Purchase    : 2,
    Rma         : 3,

    getTextName: function(variantId) {
        switch (variantId) {
        case InvoiceTypes.Sales:
        {
            return 'AR Invoice';
        }
        case InvoiceTypes.Purchase:
        {
            return 'AP Invoice';
        }
        case InvoiceTypes.Rma:
        {
            return 'Rma Invoice';
        }
        default:
        {
            throw 'Unsupported InvoiceTypes value';
        }
        }
    },

    enumerate: function (skipRma) {
        var it = this;
        var values = [];
        _.each(it, function (aValue) {
            if (Number.isNumber(aValue) && !(skipRma && aValue == InvoiceTypes.Rma)) {
                values.push({
                    value: aValue,
                    label: it.getTextName(aValue)
                });
            }
        });
        return values;
    }
};
