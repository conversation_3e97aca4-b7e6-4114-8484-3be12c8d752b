using System;
using System.Linq;
using Razor.Common.Core.DateTimeFormat;
using RazorWeb.DataModels.JqGrid.Base;
using RazorWeb.DataModels.Reports.RecyclingConsumeActions;
using RazorWeb.Helpers.Data;
using RazorWeb.Helpers.FormattingExtensions;

namespace RazorWeb.DataModels.Reports.RecyclingDailyProduction
{
    public class RecyclingConsumeActionsViewModel: BaseReportModel
    {
        public RecyclingConsumeActionsViewModel()
        {
        }

        public RecyclingConsumeActionsViewModel(JqGridRequest<RecyclingConsumeActionsFilter> request, IUserDateTimeFormatService formatService, String filterText)
        {
            Warehouses = request.Data.Warehouses == null || request.Data.Warehouses.Length == 0
                ? $"{nameof(Warehouses)} ALL"
                : $"{nameof(Warehouses)}: " + 
                String.Join(", ", request.Data.Warehouses.Select(it => it.label).ToArray());

            DataFilters = Warehouses;
            DateRangeInString = DateRangeFormattingExtensions.ToFormattedString(request.Data.Range, formatService);
            GridFilters = filterText;
        }
        
        public String Warehouses { set; get; }
        public DateTime CurrentDate { set; get; }               
    }
}