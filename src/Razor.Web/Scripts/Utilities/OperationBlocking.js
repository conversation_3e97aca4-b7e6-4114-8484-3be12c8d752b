OperationBlocking = {

    _blockings: [],

    //use only for SignalR module
    SetOperationBlocking: function(operationBlockingType, operationBlockingEntityType, operationBlockingEntityId, value) {
        var it = this;

        it.setCachedOperationBlocking(operationBlockingType, operationBlockingEntityType, operationBlockingEntityId, value);

        var blockingIdentifyAttributes =
            String.format(
                '[data-operation-blocking-type="{0}"][data-operation-blocking-entity-type="{1}"][data-operation-blocking-entity-id="{2}"]',
                operationBlockingType,
                operationBlockingEntityType,
                operationBlockingEntityId);

        if (value) {
            $(blockingIdentifyAttributes + '[data-forced-blocking]').toggleEnabled(false);
        } else {
            $(blockingIdentifyAttributes + '[data-forced-unblocking]').toggleEnabled(true);
        }
    },

    IsOperationBlockingSet: function (operationBlockingType, operationBlockingEntityType, operationBlockingEntityId) {
        var it = this;
        //var blocking = it.getCachedOperationBlocking(operationBlockingType, operationBlockingEntityType, operationBlockingEntityId);

        //if (blocking) {
        //    //if we found blocking in the cache
        //    return blocking.value;
        //}

        //otherwise get from server...
        var model = {
            operationBlockingType: operationBlockingType,
            operationBlockingEntityType: operationBlockingEntityType,
            operationBlockingEntityId: operationBlockingEntityId
        }
        var url = Common.baseUrl + '/Services/OperationBlockingService.asmx/IsOperationBlockingSet?request=';
        url += encodeURIComponent(JSON.stringify(model));

        var response = Url.GetSync(url);
        var result;
        ProcessAjaxItemResponse(response, function (data) {
            result = data;
        });
        
        //... and push result into cache
        it.setCachedOperationBlocking(operationBlockingType, operationBlockingEntityType, operationBlockingEntityId, result);

        return result;
    },

    getCachedOperationBlocking: function (operationBlockingType, operationBlockingEntityType, operationBlockingEntityId) {
        var it = this;

        var blocking = _.findWhere(
            this._blockings,
            {
                operationBlockingType: operationBlockingType,
                operationBlockingEntityType: operationBlockingEntityType,
                operationBlockingEntityId: operationBlockingEntityId
            });

        return blocking;
    },

    setCachedOperationBlocking: function (operationBlockingType, operationBlockingEntityType, operationBlockingEntityId, value) {
        var it = this;

        var blocking = _.findWhere(
            this._blockings,
            {
                operationBlockingType: operationBlockingType,
                operationBlockingEntityType: operationBlockingEntityType,
                operationBlockingEntityId: operationBlockingEntityId
            });

        if (!blocking) {
            blocking = {
                operationBlockingType: operationBlockingType,
                operationBlockingEntityType: operationBlockingEntityType,
                operationBlockingEntityId: operationBlockingEntityId
            };
            this._blockings.push(blocking);
        }
        blocking.value = value;
    },
};

OperationBlockingEntityTypes = {
    SalesOrder              : "SalesOrder",
    PurchaseOrder           : "PurchaseOrder",
    Invoice                 : "Invoice",
    InvoiceConsolidated     : "InvoiceConsolidated",
    RecyclingOrder          : "RecyclingOrder"
};

OperationBlockingTypes = {
    BlockingByConsolidateInvoice    : "BlockingByConsolidateInvoice",
    PurchaseOrderReceive: "PurchaseOrderReceive",
    SalesOrderImport: "SalesOrderImport"
};