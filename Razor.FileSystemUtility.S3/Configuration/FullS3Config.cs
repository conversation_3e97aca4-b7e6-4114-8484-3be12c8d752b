using System;
using Razor.FileSystemUtility.S3.Configuration.Base;
using Razor.FileSystemUtility.S3.Configuration.Extensions;

namespace Razor.FileSystemUtility.S3.Configuration
{
    // to be mapped from Vault.Contracts
    public class FullS3Config : IValidatedConfig
    {
        public S3ConnectionConfig Connection { set; get; }

        public BucketsConfig Buckets { set; get; }

        public void Ensure()
        {
            if (Connection == null)
                throw new ArgumentNullException(nameof(Connection));
            if (Buckets == null)
                throw new ArgumentNullException(nameof(Buckets));
            
            Connection.Ensure();
            Buckets.Ensure();
        }
    }

    public class BucketsConfig : IValidatedConfig
    {
        public string Normal { get; set; }
        public string Secured { get; set; }

        public void Ensure()
        {
            Normal.EnsureBucketName();
            Secured.EnsureBucketName();
        }
    }

    public class S3ConnectionConfig : IValidatedConfig
    {
        public string Endpoint { get; set; }
        public string AccessKey { get; set; }
        public string SecretKey { get; set; }
        public bool IsSSL { get; set; }

        public void Ensure()
        {
            Endpoint.EnsureUrl();
            AccessKey.EnsureValue();
            SecretKey.EnsureValue();
        }
    }
}